import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/Button';
import { Card, CardContent } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';
import { FileUpload } from '../components/upload/FileUpload';
import { UploadHistory } from '../components/upload/UploadHistory';
import { StatsOverview } from '../components/stats/StatsOverview';
import { UserSettings } from '../components/settings/UserSettings';
import { upload } from '../lib/api';
import { formatFileSize } from '../lib/utils';

interface UploadLimits {
  userLevel: string;
  maxDailyUploads: number;
  maxFileSize: number;
  maxStorageSpace: number;
  todayUploads: number;
  remainingUploads: number;
  canChooseProvider: boolean;
  visibleProviderCount: number;
}

interface UploadStats {
  totalUploads: number;
  totalSize: number;
  todayUploads: number;
  thisMonthUploads: number;
}

export function Dashboard() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'upload' | 'history' | 'stats' | 'settings'>('upload');
  const [limits, setLimits] = useState<UploadLimits | null>(null);
  const [stats, setStats] = useState<UploadStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const [limitsResponse, statsResponse] = await Promise.all([
        upload.getLimits(),
        upload.getStats(),
      ]);

      if (limitsResponse.success) {
        setLimits(limitsResponse.data!);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data!);
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = () => {
    loadUserData();
  };

  const handleUploadError = (error: string) => {
    console.error('上传失败:', error);
  };

  const getUserLevelBadge = (level: string) => {
    const levelMap = {
      free: { label: '免费用户', variant: 'secondary' as const },
      vip1: { label: 'VIP1', variant: 'default' as const },
      vip2: { label: 'VIP2', variant: 'warning' as const },
      vip3: { label: 'VIP3', variant: 'success' as const },
    };

    return levelMap[level as keyof typeof levelMap] || { label: level, variant: 'secondary' as const };
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 左侧边栏 */}
      <aside className="w-64 bg-white shadow-sm border-r flex flex-col">
        {/* 顶部Logo */}
        <div className="p-6 border-b">
          <div className="flex items-center space-x-3">
            <h1 className="text-lg font-bold text-gray-900">LoftChat</h1>
            <Badge variant={getUserLevelBadge(user?.userLevel || '').variant}>
              {getUserLevelBadge(user?.userLevel || '').label}
            </Badge>
          </div>
          <p className="text-xs text-gray-600 mt-1">欢迎，{user?.username}</p>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            <li>
              <button
                onClick={() => setActiveTab('upload')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'upload'
                    ? 'bg-primary-100 text-primary-700 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span>文件上传</span>
              </button>
            </li>
            <li>
              <button
                onClick={() => setActiveTab('history')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'history'
                    ? 'bg-primary-100 text-primary-700 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>上传历史</span>
              </button>
            </li>
            <li>
              <button
                onClick={() => setActiveTab('stats')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === 'stats'
                    ? 'bg-primary-100 text-primary-700 font-medium'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>统计分析</span>
              </button>
            </li>
            <li>
              <button
                onClick={() => navigate('/settings')}
                className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-gray-600 hover:bg-gray-100"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>用户设置</span>
              </button>
            </li>
          </ul>
        </nav>

        {/* 底部操作按钮 */}
        <div className="p-4 border-t space-y-2">
          {user?.role === 'admin' && (
            <Button
              variant="default"
              size="sm"
              onClick={() => navigate('/admin')}
              className="w-full bg-red-600 hover:bg-red-700 text-white"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              管理端
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={logout} className="w-full">
            退出登录
          </Button>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col">
        {/* 顶部标题栏 */}
        <header className="bg-white shadow-sm border-b px-6 py-4">
          <h2 className="text-base font-semibold text-gray-900">
            {activeTab === 'upload' && '文件上传'}
            {activeTab === 'history' && '上传历史'}
            {activeTab === 'stats' && '统计分析'}
            {activeTab === 'settings' && '用户设置'}
          </h2>
        </header>

        {/* 内容区域 */}
        <div className="flex-1 p-6 overflow-auto">
          {/* 页面内容 */}
          <div className="space-y-6">
            {activeTab === 'upload' && (
              <FileUpload
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
                maxFiles={limits?.maxDailyUploads || 10}
                maxFileSize={limits?.maxFileSize || 100 * 1024 * 1024}
              />
            )}

            {activeTab === 'history' && (
              <UploadHistory />
            )}

            {activeTab === 'stats' && (
              <StatsOverview />
            )}

            {activeTab === 'settings' && (
              <UserSettings />
            )}
          </div>
        </div>
      </main>
    </div>
  );
}