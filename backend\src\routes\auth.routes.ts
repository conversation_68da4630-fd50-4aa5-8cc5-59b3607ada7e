import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller';
import { authenticateToken, requestId, recordIP } from '../middleware/auth.middleware';
import { validateJoiRequest } from '../middleware/validation.middleware';
import { authValidation } from '../utils/validation.schemas';

const router = Router();

// 应用通用中间件
router.use(requestId);
router.use(recordIP);

/**
 * @route   POST /api/auth/register
 * @desc    用户注册
 * @access  Public
 */
router.post('/register',
  validateJoiRequest(authValidation.register),
  AuthController.register
);

/**
 * @route   POST /api/auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post('/login',
  validateJoiRequest(authValidation.login),
  AuthController.login
);

/**
 * @route   GET /api/auth/me
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get('/me',
  authenticateToken,
  AuthController.getCurrentUser
);

/**
 * @route   POST /api/auth/logout
 * @desc    用户登出
 * @access  Private
 */
router.post('/logout',
  authenticateToken,
  AuthController.logout
);

/**
 * @route   POST /api/auth/refresh
 * @desc    刷新访问令牌
 * @access  Private
 */
router.post('/refresh',
  authenticateToken,
  AuthController.refreshToken
);

/**
 * @route   POST /api/auth/change-password
 * @desc    修改密码
 * @access  Private
 */
router.post('/change-password',
  authenticateToken,
  validateJoiRequest(authValidation.changePassword),
  AuthController.changePassword
);

/**
 * @route   POST /api/auth/forgot-password
 * @desc    忘记密码
 * @access  Public
 */
router.post('/forgot-password',
  validateJoiRequest(authValidation.forgotPassword),
  AuthController.forgotPassword
);

/**
 * @route   POST /api/auth/reset-password
 * @desc    重置密码
 * @access  Public
 */
router.post('/reset-password',
  validateJoiRequest(authValidation.resetPassword),
  AuthController.resetPassword
);

/**
 * @route   GET /api/auth/verify-email/:token
 * @desc    验证邮箱
 * @access  Public
 */
router.get('/verify-email/:token',
  AuthController.verifyEmail
);

/**
 * @route   POST /api/auth/resend-verification
 * @desc    重新发送验证邮件
 * @access  Private
 */
router.post('/resend-verification',
  authenticateToken,
  AuthController.resendVerification
);

export default router;
