{"name": "loftchat-backend", "version": "1.0.0", "description": "LoftChat智能图片上传管理分发系统后端", "main": "dist/app.js", "type": "commonjs", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:seed": "ts-node prisma/seed.ts"}, "keywords": ["image", "upload", "management", "distribution", "nodejs", "typescript", "express"], "author": "LoftChat Team", "license": "MIT", "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.6.0", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.23", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/pg": "^8.10.9", "@types/socket.io": "^3.0.1", "@types/uuid": "^9.0.7", "axios": "^1.6.2", "bcrypt": "^5.1.1", "bullmq": "^4.15.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "geoip-lite": "^1.4.10", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "prisma": "^5.6.0", "sharp": "^0.33.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "typescript": "^5.8.3", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1"}}