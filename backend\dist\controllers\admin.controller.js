"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
const log_service_1 = require("../services/log.service");
class AdminController {
    static async getSystemStats(req, res) {
        try {
            const totalUsers = await database_1.prisma.user.count();
            const activeUsers = await database_1.prisma.user.count({
                where: {
                    status: 'active'
                }
            });
            const totalUploads = await database_1.prisma.userImage.count();
            const todayUploads = await database_1.prisma.userImage.count({
                where: {
                    createdAt: {
                        gte: new Date(new Date().setHours(0, 0, 0, 0))
                    }
                }
            });
            const storageStats = await database_1.prisma.image.aggregate({
                _sum: {
                    fileSize: true
                },
                where: {
                    isDeleted: false
                }
            });
            const systemLoad = {
                cpu: Math.floor(Math.random() * 100),
                memory: Math.floor(Math.random() * 100),
                disk: Math.floor(Math.random() * 100)
            };
            const onlineUsers = Math.floor(Math.random() * 50);
            const stats = {
                totalUsers,
                activeUsers,
                totalUploads,
                todayUploads,
                totalStorage: storageStats._sum.fileSize || 0,
                systemLoad,
                onlineUsers
            };
            res.json({
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取系统统计失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取系统统计失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUserLevel(req, res) {
        try {
            const { id } = req.params;
            const { level, expiresAt } = req.body;
            const validLevels = ['free', 'vip1', 'vip2', 'vip3'];
            if (!validLevels.includes(level)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的用户等级',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: Number(id) }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (user.role === 'admin') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.FORBIDDEN,
                        message: '不能修改管理员的等级',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: Number(id) },
                data: {
                    userLevel: level,
                    levelExpiresAt: expiresAt ? new Date(expiresAt) : null
                },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    userLevel: true,
                    levelExpiresAt: true
                }
            });
            res.json({
                success: true,
                data: updatedUser,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户等级失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户等级失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserDetails(req, res) {
        try {
            const { id } = req.params;
            const user = await database_1.prisma.user.findUnique({
                where: { id: Number(id) },
                include: {
                    userImages: {
                        include: {
                            image: true
                        },
                        orderBy: {
                            createdAt: 'desc'
                        },
                        take: 10
                    }
                }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            res.json({
                success: true,
                data: user,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户详情失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户详情失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getRealtimeLogs(req, res) {
        try {
            const { type = 'all', limit = 100 } = req.query;
            let logs = [];
            if (type === 'all' || type === 'system') {
                const systemLogs = await log_service_1.LogService.getSystemLogs({
                    page: 1,
                    limit: parseInt(limit) || 100,
                });
                logs = [...logs, ...systemLogs.logs.map(log => ({ ...log, type: 'system' }))];
            }
            if (type === 'all' || type === 'upload') {
                const uploadLogs = await log_service_1.LogService.getUploadLogs({
                    page: 1,
                    limit: parseInt(limit) || 100,
                });
                logs = [...logs, ...uploadLogs.logs.map(log => ({ ...log, type: 'upload' }))];
            }
            logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            res.json({
                success: true,
                data: logs.slice(0, parseInt(limit) || 100),
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取实时日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取实时日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getSystemLogs(req, res) {
        try {
            const { page = 1, limit = 50, levels, types, startDate, endDate } = req.query;
            const filters = {};
            if (levels) {
                filters.levels = levels.split(',');
            }
            if (types) {
                filters.types = types.split(',');
            }
            if (startDate) {
                filters.startDate = startDate;
            }
            if (endDate) {
                filters.endDate = endDate;
            }
            const result = await log_service_1.LogService.getSystemLogs({
                page: parseInt(page),
                limit: parseInt(limit),
                filters,
            });
            res.json({
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取系统日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取系统日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUsers(req, res) {
        try {
            const { page = 1, limit = 20, search, status, role, userLevel, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const where = {};
            if (search) {
                where.OR = [
                    { username: { contains: search, mode: 'insensitive' } },
                    { email: { contains: search, mode: 'insensitive' } },
                    { displayName: { contains: search, mode: 'insensitive' } }
                ];
            }
            if (status) {
                where.status = status;
            }
            if (role) {
                where.role = role;
            }
            if (userLevel) {
                where.userLevel = userLevel;
            }
            const orderBy = {};
            orderBy[sortBy] = sortOrder;
            const [users, total] = await Promise.all([
                database_1.prisma.user.findMany({
                    where,
                    orderBy,
                    skip,
                    take,
                    select: {
                        id: true,
                        username: true,
                        email: true,
                        displayName: true,
                        role: true,
                        userLevel: true,
                        status: true,
                        avatarUrl: true,
                        createdAt: true,
                        updatedAt: true,
                        lastLoginAt: true,
                        lastActiveAt: true,
                        levelExpiresAt: true,
                        _count: {
                            select: {
                                userImages: true,
                                uploadLogs: true
                            }
                        }
                    }
                }),
                database_1.prisma.user.count({ where })
            ]);
            res.json({
                success: true,
                data: {
                    users,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户列表失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户列表失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserDetail(req, res) {
        try {
            const { userId } = req.params;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                include: {
                    userSettings: true,
                    _count: {
                        select: {
                            userImages: true,
                            uploadLogs: true,
                            activityLogs: true,
                            loginHistory: true
                        }
                    }
                }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            await log_service_1.LogService.logAdminOperation(adminId, 'view_user_detail', 'user', user.id, { viewedUser: user.username }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: user,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户详情失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户详情失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUserStatus(req, res) {
        try {
            const { userId } = req.params;
            const { status, reason } = req.body;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const validStatuses = ['active', 'suspended', 'banned'];
            if (!validStatuses.includes(status)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的用户状态',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { id: true, username: true, status: true, role: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (user.role === 'admin') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.PERMISSION_DENIED,
                        message: '不能修改管理员的状态',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: parseInt(userId) },
                data: {
                    status,
                    updatedAt: new Date()
                },
                select: {
                    id: true,
                    username: true,
                    status: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logAdminOperation(adminId, 'update_user_status', 'user', user.id, {
                oldStatus: user.status,
                newStatus: status,
                reason,
                targetUser: user.username
            }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: updatedUser,
                message: '用户状态更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户状态失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户状态失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUserProfile(req, res) {
        try {
            const { userId } = req.params;
            const { displayName, bio, location, website, profileVisibility, allowDirectMessages, showOnlineStatus } = req.body;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { id: true, username: true, role: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (user.role === 'admin') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.PERMISSION_DENIED,
                        message: '不能修改管理员的信息',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: parseInt(userId) },
                data: {
                    displayName: displayName || null,
                    bio: bio || null,
                    location: location || null,
                    website: website || null,
                    profileVisibility: profileVisibility || undefined,
                    allowDirectMessages: allowDirectMessages !== undefined ? allowDirectMessages : undefined,
                    showOnlineStatus: showOnlineStatus !== undefined ? showOnlineStatus : undefined,
                    updatedAt: new Date()
                },
                select: {
                    id: true,
                    username: true,
                    displayName: true,
                    bio: true,
                    location: true,
                    website: true,
                    profileVisibility: true,
                    allowDirectMessages: true,
                    showOnlineStatus: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logAdminOperation(adminId, 'update_user_profile', 'user', user.id, {
                changes: req.body,
                targetUser: user.username
            }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: updatedUser,
                message: '用户信息更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户信息失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户信息失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserActivityLogs(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 50 } = req.query;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const [logs, total] = await Promise.all([
                database_1.prisma.userActivityLog.findMany({
                    where: { userId: parseInt(userId) },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                    select: {
                        id: true,
                        activityType: true,
                        activityData: true,
                        ipAddress: true,
                        location: true,
                        deviceInfo: true,
                        createdAt: true
                    }
                }),
                database_1.prisma.userActivityLog.count({ where: { userId: parseInt(userId) } })
            ]);
            await log_service_1.LogService.logAdminOperation(adminId, 'view_user_activity_logs', 'user', parseInt(userId), { viewedLogsCount: logs.length }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: {
                    logs,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户活动日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户活动日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserLoginHistory(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 50 } = req.query;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const [history, total] = await Promise.all([
                database_1.prisma.userLoginHistory.findMany({
                    where: { userId: parseInt(userId) },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                    select: {
                        id: true,
                        loginType: true,
                        isSuccess: true,
                        failureReason: true,
                        ipAddress: true,
                        location: true,
                        deviceInfo: true,
                        createdAt: true
                    }
                }),
                database_1.prisma.userLoginHistory.count({ where: { userId: parseInt(userId) } })
            ]);
            await log_service_1.LogService.logAdminOperation(adminId, 'view_user_login_history', 'user', parseInt(userId), { viewedHistoryCount: history.length }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: {
                    history,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户登录历史失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户登录历史失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getLevelApplications(req, res) {
        try {
            const { page = 1, limit = 20, status = 'all', requestedLevel, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const where = {};
            if (status !== 'all') {
                where.status = status;
            }
            if (requestedLevel && requestedLevel !== 'all') {
                where.requestedLevel = requestedLevel;
            }
            const orderBy = {};
            orderBy[sortBy] = sortOrder;
            const [applications, total] = await Promise.all([
                database_1.prisma.userLevelApplication.findMany({
                    where,
                    orderBy,
                    skip,
                    take,
                    include: {
                        user: {
                            select: {
                                id: true,
                                username: true,
                                email: true,
                                displayName: true,
                                userLevel: true,
                                createdAt: true
                            }
                        },
                        admin: {
                            select: {
                                username: true,
                                displayName: true
                            }
                        }
                    }
                }),
                database_1.prisma.userLevelApplication.count({ where })
            ]);
            res.json({
                success: true,
                data: {
                    applications,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取等级申请列表失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取等级申请列表失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async processLevelApplication(req, res) {
        try {
            const { applicationId } = req.params;
            const { action, comment } = req.body;
            const adminId = parseInt(req.user.id);
            if (!applicationId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '申请ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (!['approve', 'reject'].includes(action)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的操作类型',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const application = await database_1.prisma.userLevelApplication.findUnique({
                where: { id: parseInt(applicationId) },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            userLevel: true,
                            email: true
                        }
                    }
                }
            });
            if (!application) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '申请记录不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (application.status !== 'pending') {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '该申请已被处理',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const newStatus = action === 'approve' ? 'approved' : 'rejected';
            const processedAt = new Date();
            const result = await database_1.prisma.$transaction(async (tx) => {
                const updatedApplication = await tx.userLevelApplication.update({
                    where: { id: parseInt(applicationId) },
                    data: {
                        status: newStatus,
                        adminId,
                        adminComment: comment || null,
                        processedAt,
                        updatedAt: new Date()
                    }
                });
                if (action === 'approve') {
                    await tx.user.update({
                        where: { id: application.userId },
                        data: {
                            userLevel: application.requestedLevel,
                            levelExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
                            updatedAt: new Date()
                        }
                    });
                    await tx.userLevelHistory.create({
                        data: {
                            userId: application.userId,
                            oldLevel: application.currentLevel,
                            newLevel: application.requestedLevel,
                            changedBy: adminId,
                            reason: `等级申请批准: ${comment || '无备注'}`
                        }
                    });
                }
                return updatedApplication;
            });
            await log_service_1.LogService.logAdminOperation(adminId, `level_application_${action}`, 'level_application', parseInt(applicationId), {
                targetUser: application.user.username,
                currentLevel: application.currentLevel,
                requestedLevel: application.requestedLevel,
                comment: comment || null
            }, req.ip, req.get('User-Agent'));
            await log_service_1.LogService.logSystemEvent(`level_application_${action}`, 'info', `管理员${action === 'approve' ? '批准' : '拒绝'}了用户 ${application.user.username} 的等级申请`, {
                applicationId: parseInt(applicationId),
                userId: application.userId,
                adminId,
                currentLevel: application.currentLevel,
                requestedLevel: application.requestedLevel,
                comment: comment || null
            });
            res.json({
                success: true,
                data: result,
                message: `申请已${action === 'approve' ? '批准' : '拒绝'}`,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('处理等级申请失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '处理等级申请失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getProviders(req, res) {
        res.json({ success: true, data: [], timestamp: new Date().toISOString() });
    }
    static async createProvider(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async updateProvider(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async deleteProvider(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getSystemMonitoring(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getIPSecurity(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async addIPToBlacklist(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async removeIPFromBlacklist(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getAnalyticsOverview(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getUserAnalytics(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getUploadAnalytics(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
}
exports.AdminController = AdminController;
//# sourceMappingURL=admin.controller.js.map