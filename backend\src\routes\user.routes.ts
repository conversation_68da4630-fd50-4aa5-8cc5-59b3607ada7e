import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { body, query } from 'express-validator';

const router = Router();

// 所有用户路由都需要认证
router.use(authenticateToken);

// 获取用户个人信息
router.get('/profile', UserController.getProfile);

// 更新用户个人信息
router.put('/profile', [
  body('displayName')
    .optional()
    .isLength({ max: 100 })
    .withMessage('显示名称不能超过100个字符'),
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('个人简介不能超过500个字符'),
  body('location')
    .optional()
    .isLength({ max: 100 })
    .withMessage('位置不能超过100个字符'),
  body('website')
    .optional()
    .isURL()
    .withMessage('请输入有效的网站URL'),
  body('profileVisibility')
    .optional()
    .isIn(['public', 'private', 'friends'])
    .withMessage('无效的隐私设置'),
  body('allowDirectMessages')
    .optional()
    .isBoolean()
    .withMessage('允许私信必须是布尔值'),
  body('showOnlineStatus')
    .optional()
    .isBoolean()
    .withMessage('显示在线状态必须是布尔值'),
  validateRequest
], UserController.updateProfile);

// 修改密码
router.put('/password', [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码是必需的'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密码长度至少为6位')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('新密码必须包含字母和数字'),
  validateRequest
], UserController.changePassword);

// 更新通知设置
router.put('/notifications', [
  body('emailNotifications')
    .optional()
    .isBoolean()
    .withMessage('邮件通知必须是布尔值'),
  body('pushNotifications')
    .optional()
    .isBoolean()
    .withMessage('推送通知必须是布尔值'),
  body('marketingEmails')
    .optional()
    .isBoolean()
    .withMessage('营销邮件必须是布尔值'),
  body('loginNotifications')
    .optional()
    .isBoolean()
    .withMessage('登录通知必须是布尔值'),
  validateRequest
], UserController.updateNotificationSettings);

// 更新上传设置
router.put('/upload-settings', [
  body('defaultImageQuality')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('图片质量必须在1-100之间'),
  body('autoCompress')
    .optional()
    .isBoolean()
    .withMessage('自动压缩必须是布尔值'),
  body('defaultImageFormat')
    .optional()
    .isIn(['original', 'webp', 'jpeg', 'png'])
    .withMessage('无效的图片格式'),
  body('maxImageSize')
    .optional()
    .isInt({ min: 1024, max: 50 * 1024 * 1024 })
    .withMessage('最大图片大小必须在1KB-50MB之间'),
  validateRequest
], UserController.updateUploadSettings);

// 获取用户活动日志
router.get('/activity-logs', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('activityType')
    .optional()
    .isString()
    .withMessage('活动类型必须是字符串'),
  validateRequest
], UserController.getActivityLogs);

// 获取登录历史
router.get('/login-history', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  validateRequest
], UserController.getLoginHistory);

export default router;
