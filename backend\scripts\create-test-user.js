const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('🔄 创建测试用户...');

    // 创建测试用户账户
    const testEmail = '<EMAIL>';
    const testPassword = 'test123456';
    const hashedPassword = await bcrypt.hash(testPassword, 12);

    const testUser = await prisma.user.upsert({
      where: { email: testEmail },
      update: {},
      create: {
        username: 'testuser',
        email: testEmail,
        passwordHash: hashedPassword,
        role: 'user',
        userLevel: 'free',
        status: 'active',
        displayName: '测试用户',
        bio: '这是一个测试用户账户，用于验证设置功能',
        location: '北京',
        website: 'https://example.com',
        profileVisibility: 'public',
        allowDirectMessages: true,
        showOnlineStatus: true,
        emailNotifications: true,
        pushNotifications: true,
        marketingEmails: false,
        defaultImageQuality: 80,
        autoCompress: true,
        defaultImageFormat: 'original',
        maxImageSize: 10 * 1024 * 1024, // 10MB
        twoFactorEnabled: false,
        loginNotifications: true,
        levelExpiresAt: null
      }
    });

    console.log(`✅ 已创建测试用户: ${testEmail} / ${testPassword}`);
    console.log(`用户ID: ${testUser.id}`);
    console.log(`用户名: ${testUser.username}`);
    console.log(`显示名称: ${testUser.displayName}`);
    console.log(`个人简介: ${testUser.bio}`);
    console.log(`位置: ${testUser.location}`);
    console.log(`网站: ${testUser.website}`);

    // 创建管理员账户
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456';
    const adminHashedPassword = await bcrypt.hash(adminPassword, 12);

    const adminUser = await prisma.user.upsert({
      where: { email: adminEmail },
      update: {},
      create: {
        username: 'admin',
        email: adminEmail,
        passwordHash: adminHashedPassword,
        role: 'admin',
        userLevel: 'vip3',
        status: 'active',
        displayName: '系统管理员',
        bio: '系统默认管理员账户',
        profileVisibility: 'public',
        allowDirectMessages: true,
        showOnlineStatus: true,
        emailNotifications: true,
        pushNotifications: true,
        marketingEmails: false,
        defaultImageQuality: 85,
        autoCompress: true,
        defaultImageFormat: 'webp',
        maxImageSize: 50 * 1024 * 1024, // 50MB
        twoFactorEnabled: false,
        loginNotifications: true,
        levelExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1年后过期
      }
    });

    console.log(`✅ 已创建管理员账户: ${adminEmail} / ${adminPassword}`);

    console.log('\n🎉 用户创建完成！');
    console.log('\n📋 登录信息:');
    console.log(`测试用户: ${testEmail} / ${testPassword}`);
    console.log(`管理员: ${adminEmail} / ${adminPassword}`);
    console.log('\n🚀 现在可以使用这些账户登录并测试设置功能了！');

  } catch (error) {
    console.error('❌ 创建用户失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行创建脚本
createTestUser();
