import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/Button';
import { Card, CardContent } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

// 管理端页面组件
import { AdminOverview } from '../components/admin/AdminOverview';
import { UserManagement } from '../components/admin/UserManagement';
import { LevelApplicationManagement } from '../components/admin/LevelApplicationManagement';
import { ProviderManagement } from '../components/admin/ProviderManagement';
import { SystemMonitoring } from '../components/admin/SystemMonitoring';
import { SecurityManagement } from '../components/admin/SecurityManagement';
import { LogsManagement } from '../components/admin/LogsManagement';
import { AnalyticsManagement } from '../components/admin/AnalyticsManagement';

type AdminTab = 'overview' | 'users' | 'level-applications' | 'providers' | 'monitoring' | 'security' | 'logs' | 'analytics';

export function AdminDashboard() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<AdminTab>('overview');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 严格的管理员权限检查
    if (!user) {
      // 如果用户未登录，重定向到登录页
      navigate('/', { replace: true });
      return;
    }

    if (user.role !== 'admin') {
      // 如果不是管理员，强制重定向到普通用户页面
      console.warn('非管理员用户尝试访问管理端，已重定向');
      navigate('/', { replace: true });
      return;
    }

    setLoading(false);
  }, [user, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
          <p className="text-gray-600">加载管理端...</p>
        </div>
      </div>
    );
  }

  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">访问被拒绝</h1>
          <p className="text-gray-600 mb-4">您没有访问管理端的权限</p>
          <Button onClick={() => window.location.href = '/'}>返回首页</Button>
        </div>
      </div>
    );
  }

  const menuItems = [
    {
      key: 'overview' as AdminTab,
      label: '实时概览',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      description: '系统实时状态和上传日志'
    },
    {
      key: 'users' as AdminTab,
      label: '用户管理',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      description: '用户账号、等级和权限管理'
    },
    {
      key: 'level-applications' as AdminTab,
      label: '等级申请',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
        </svg>
      ),
      description: '用户等级升级申请审核'
    },
    {
      key: 'providers' as AdminTab,
      label: '接口管理',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
        </svg>
      ),
      description: '第三方接口配置和监控'
    },
    {
      key: 'monitoring' as AdminTab,
      label: '系统监控',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      description: '系统性能和健康状态监控'
    },
    {
      key: 'security' as AdminTab,
      label: '安全管理',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      description: 'IP安全、风控规则和黑白名单'
    },
    {
      key: 'logs' as AdminTab,
      label: '日志管理',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      description: '系统日志查看和分析'
    },
    {
      key: 'analytics' as AdminTab,
      label: '数据分析',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      description: 'UV/PV统计和用户行为分析'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 左侧边栏 */}
      <aside className="w-72 bg-white shadow-sm border-r flex flex-col">
        {/* 顶部Logo */}
        <div className="p-6 border-b">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div>
              <h1 className="text-base font-bold text-gray-900">LoftChat 管理端</h1>
              <p className="text-xs text-gray-500">系统管理控制台</p>
            </div>
          </div>
          <div className="mt-3 flex items-center space-x-2">
            <Badge variant="success">管理员</Badge>
            <span className="text-xs text-gray-600">{user?.username}</span>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.key}>
                <button
                  onClick={() => setActiveTab(item.key)}
                  className={`w-full flex items-start space-x-3 px-3 py-3 rounded-lg text-left transition-colors ${
                    activeTab === item.key
                      ? 'bg-red-50 text-red-700 border border-red-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <div className="flex-shrink-0 mt-0.5">
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
                  </div>
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* 底部操作 */}
        <div className="p-4 border-t space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/')}
            className="w-full"
          >
            返回用户端
          </Button>
          <Button variant="outline" size="sm" onClick={logout} className="w-full">
            退出登录
          </Button>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col">
        {/* 顶部标题栏 */}
        <header className="bg-white shadow-sm border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-base font-semibold text-gray-900">
                {menuItems.find(item => item.key === activeTab)?.label}
              </h2>
              <p className="text-xs text-gray-600 mt-1">
                {menuItems.find(item => item.key === activeTab)?.description}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-xs text-gray-500">
                最后更新: {new Date().toLocaleString()}
              </div>
            </div>
          </div>
        </header>

        {/* 内容区域 */}
        <div className="flex-1 p-6 overflow-auto">
          {activeTab === 'overview' && <AdminOverview />}
          {activeTab === 'users' && <UserManagement />}
          {activeTab === 'level-applications' && <LevelApplicationManagement />}
          {activeTab === 'providers' && <ProviderManagement />}
          {activeTab === 'monitoring' && <SystemMonitoring />}
          {activeTab === 'security' && <SecurityManagement />}
          {activeTab === 'logs' && <LogsManagement />}
          {activeTab === 'analytics' && <AnalyticsManagement />}
        </div>
      </main>
    </div>
  );
}
