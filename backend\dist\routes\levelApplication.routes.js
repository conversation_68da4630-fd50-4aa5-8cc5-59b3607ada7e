"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const levelApplication_controller_1 = require("../controllers/levelApplication.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authenticateToken);
router.use((req, res, next) => {
    console.log(`[LevelApplication] ${req.method} ${req.path}`, {
        query: req.query,
        body: req.body,
        userId: req.user?.id
    });
    next();
});
router.get('/configs', levelApplication_controller_1.LevelApplicationController.getLevelConfigs);
router.post('/submit', [
    (0, express_validator_1.body)('requestedLevel')
        .notEmpty()
        .withMessage('申请等级是必需的')
        .isIn(['free', 'vip1', 'vip2', 'vip3'])
        .withMessage('无效的等级申请'),
    (0, express_validator_1.body)('reason')
        .notEmpty()
        .withMessage('申请理由是必需的')
        .isLength({ min: 10, max: 1000 })
        .withMessage('申请理由长度应在10-1000字符之间'),
    (0, express_validator_1.body)('contactInfo')
        .optional()
        .isLength({ max: 200 })
        .withMessage('联系方式不能超过200个字符'),
    (0, express_validator_1.body)('businessInfo')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('业务信息不能超过1000个字符'),
    (0, express_validator_1.body)('expectedUsage')
        .optional()
        .isLength({ max: 500 })
        .withMessage('预期使用量不能超过500个字符'),
    validation_middleware_1.validateRequest
], levelApplication_controller_1.LevelApplicationController.submitApplication);
router.get('/my-applications', [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('每页数量必须在1-50之间'),
    validation_middleware_1.validateRequest
], levelApplication_controller_1.LevelApplicationController.getUserApplications);
router.put('/:applicationId/cancel', [
    (0, express_validator_1.param)('applicationId')
        .isInt({ min: 1 })
        .withMessage('申请ID必须是正整数'),
    validation_middleware_1.validateRequest
], levelApplication_controller_1.LevelApplicationController.cancelApplication);
exports.default = router;
//# sourceMappingURL=levelApplication.routes.js.map