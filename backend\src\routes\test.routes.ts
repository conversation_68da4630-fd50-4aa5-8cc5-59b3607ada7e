import { Router } from 'express';
import { LogService } from '../services/log.service';

const router = Router();

// 测试日志生成路由
router.post('/logs/generate', async (req, res) => {
  try {
    // 生成一些测试日志
    await LogService.logSystemEvent(
      'test_event',
      'info',
      '这是一个测试系统日志',
      { testData: 'Hello World', timestamp: new Date().toISOString() }
    );

    await LogService.logUploadAction(
      1, // userId
      123, // imageId
      'upload',
      'test-image.jpg',
      1024 * 1024, // 1MB
      req.ip,
      req.get('User-Agent'),
      true
    );

    await LogService.logSystemEvent(
      'server_status',
      'warning',
      '服务器负载较高',
      { cpu: 85, memory: 78, disk: 45 }
    );

    await LogService.logAdminOperation(
      1, // adminId
      'test_operation',
      'user',
      123,
      { action: 'test', details: 'This is a test admin operation' },
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      message: '测试日志已生成',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('生成测试日志失败:', error);
    res.status(500).json({
      success: false,
      error: '生成测试日志失败'
    });
  }
});

export default router;
