"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
const bcrypt_1 = __importDefault(require("bcrypt"));
const log_service_1 = require("../services/log.service");
class UserController {
    static async getProfile(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    role: true,
                    userLevel: true,
                    status: true,
                    avatarUrl: true,
                    displayName: true,
                    bio: true,
                    location: true,
                    website: true,
                    profileVisibility: true,
                    allowDirectMessages: true,
                    showOnlineStatus: true,
                    emailNotifications: true,
                    pushNotifications: true,
                    marketingEmails: true,
                    defaultImageQuality: true,
                    autoCompress: true,
                    defaultImageFormat: true,
                    maxImageSize: true,
                    twoFactorEnabled: true,
                    loginNotifications: true,
                    createdAt: true,
                    updatedAt: true,
                    lastLoginAt: true,
                    lastActiveAt: true,
                    levelExpiresAt: true,
                }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            res.json({
                success: true,
                data: user,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户信息失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateProfile(req, res) {
        try {
            const userId = parseInt(req.user.id);
            console.log('更新用户信息 - 用户ID:', userId);
            console.log('更新用户信息 - 请求体:', req.body);
            const { displayName, bio, location, website, profileVisibility, allowDirectMessages, showOnlineStatus } = req.body;
            if (displayName && displayName.length > 100) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '显示名称不能超过100个字符',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (bio && bio.length > 500) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '个人简介不能超过500个字符',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: userId },
                data: {
                    displayName: displayName || null,
                    bio: bio || null,
                    location: location || null,
                    website: website || null,
                    profileVisibility: profileVisibility || undefined,
                    allowDirectMessages: allowDirectMessages !== undefined ? allowDirectMessages : undefined,
                    showOnlineStatus: showOnlineStatus !== undefined ? showOnlineStatus : undefined,
                    updatedAt: new Date()
                },
                select: {
                    id: true,
                    username: true,
                    displayName: true,
                    bio: true,
                    location: true,
                    website: true,
                    profileVisibility: true,
                    allowDirectMessages: true,
                    showOnlineStatus: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logSystemEvent('user_profile_update', 'info', `用户 ${updatedUser.username} 更新了个人信息`, { userId, changes: req.body });
            res.json({
                success: true,
                data: updatedUser,
                message: '个人信息更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户信息失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户信息失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async changePassword(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { currentPassword, newPassword } = req.body;
            if (!currentPassword || !newPassword) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '当前密码和新密码都是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (newPassword.length < 6) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '新密码长度至少为6位',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, username: true, passwordHash: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const isCurrentPasswordValid = await bcrypt_1.default.compare(currentPassword, user.passwordHash);
            if (!isCurrentPasswordValid) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_CREDENTIALS,
                        message: '当前密码不正确',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const saltRounds = 12;
            const newPasswordHash = await bcrypt_1.default.hash(newPassword, saltRounds);
            await database_1.prisma.user.update({
                where: { id: userId },
                data: {
                    passwordHash: newPasswordHash,
                    lastPasswordChange: new Date(),
                    updatedAt: new Date()
                }
            });
            await log_service_1.LogService.logSystemEvent('user_password_change', 'info', `用户 ${user.username} 修改了密码`, { userId, ipAddress: req.ip });
            res.json({
                success: true,
                message: '密码修改成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('修改密码失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '修改密码失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateNotificationSettings(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { emailNotifications, pushNotifications, marketingEmails, loginNotifications } = req.body;
            const updatedUser = await database_1.prisma.user.update({
                where: { id: userId },
                data: {
                    emailNotifications: emailNotifications !== undefined ? emailNotifications : undefined,
                    pushNotifications: pushNotifications !== undefined ? pushNotifications : undefined,
                    marketingEmails: marketingEmails !== undefined ? marketingEmails : undefined,
                    loginNotifications: loginNotifications !== undefined ? loginNotifications : undefined,
                    updatedAt: new Date()
                },
                select: {
                    emailNotifications: true,
                    pushNotifications: true,
                    marketingEmails: true,
                    loginNotifications: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logSystemEvent('user_notification_settings_update', 'info', `用户更新了通知设置`, { userId, changes: req.body });
            res.json({
                success: true,
                data: updatedUser,
                message: '通知设置更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新通知设置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新通知设置失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUploadSettings(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { defaultImageQuality, autoCompress, defaultImageFormat, maxImageSize } = req.body;
            if (defaultImageQuality && (defaultImageQuality < 1 || defaultImageQuality > 100)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '图片质量必须在1-100之间',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (maxImageSize && maxImageSize > 50 * 1024 * 1024) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '最大图片大小不能超过50MB',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const validFormats = ['original', 'webp', 'jpeg', 'png'];
            if (defaultImageFormat && !validFormats.includes(defaultImageFormat)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的图片格式',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: userId },
                data: {
                    defaultImageQuality: defaultImageQuality || undefined,
                    autoCompress: autoCompress !== undefined ? autoCompress : undefined,
                    defaultImageFormat: defaultImageFormat || undefined,
                    maxImageSize: maxImageSize || undefined,
                    updatedAt: new Date()
                },
                select: {
                    defaultImageQuality: true,
                    autoCompress: true,
                    defaultImageFormat: true,
                    maxImageSize: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logSystemEvent('user_upload_settings_update', 'info', `用户更新了上传偏好设置`, { userId, changes: req.body });
            res.json({
                success: true,
                data: updatedUser,
                message: '上传设置更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新上传设置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新上传设置失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getActivityLogs(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { page = 1, limit = 20, activityType } = req.query;
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const where = { userId };
            if (activityType) {
                where.activityType = activityType;
            }
            const [logs, total] = await Promise.all([
                database_1.prisma.userActivityLog.findMany({
                    where,
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                    select: {
                        id: true,
                        activityType: true,
                        activityData: true,
                        ipAddress: true,
                        location: true,
                        deviceInfo: true,
                        createdAt: true
                    }
                }),
                database_1.prisma.userActivityLog.count({ where })
            ]);
            res.json({
                success: true,
                data: {
                    logs,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取活动日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取活动日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getLoginHistory(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { page = 1, limit = 20 } = req.query;
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const [history, total] = await Promise.all([
                database_1.prisma.userLoginHistory.findMany({
                    where: { userId },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                    select: {
                        id: true,
                        loginType: true,
                        isSuccess: true,
                        failureReason: true,
                        ipAddress: true,
                        location: true,
                        deviceInfo: true,
                        createdAt: true
                    }
                }),
                database_1.prisma.userLoginHistory.count({ where: { userId } })
            ]);
            res.json({
                success: true,
                data: {
                    history,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取登录历史失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取登录历史失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
}
exports.UserController = UserController;
//# sourceMappingURL=user.controller.js.map