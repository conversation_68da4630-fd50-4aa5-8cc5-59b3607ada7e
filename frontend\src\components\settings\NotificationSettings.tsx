import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface UserProfile {
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  loginNotifications: boolean;
}

interface NotificationSettingsProps {
  userProfile: UserProfile;
  onUpdate: (updatedData: Partial<UserProfile>) => void;
}

export function NotificationSettings({ userProfile, onUpdate }: NotificationSettingsProps) {
  const [formData, setFormData] = useState({
    emailNotifications: userProfile.emailNotifications,
    pushNotifications: userProfile.pushNotifications,
    marketingEmails: userProfile.marketingEmails,
    loginNotifications: userProfile.loginNotifications,
  });
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleToggle = (field: keyof typeof formData) => {
    const newValue = !formData[field];
    setFormData(prev => ({
      ...prev,
      [field]: newValue
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch('/api/user/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('更新失败');
      }

      const data = await response.json();
      if (data.success) {
        onUpdate(data.data);
        setHasChanges(false);
      } else {
        throw new Error(data.error?.message || '更新失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      alert(error instanceof Error ? error.message : '保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setFormData({
      emailNotifications: userProfile.emailNotifications,
      pushNotifications: userProfile.pushNotifications,
      marketingEmails: userProfile.marketingEmails,
      loginNotifications: userProfile.loginNotifications,
    });
    setHasChanges(false);
  };

  const notificationItems = [
    {
      key: 'emailNotifications' as const,
      title: '邮件通知',
      description: '接收重要的系统通知和更新',
      icon: '📧',
      category: '系统通知'
    },
    {
      key: 'pushNotifications' as const,
      title: '推送通知',
      description: '在浏览器中接收实时通知',
      icon: '🔔',
      category: '系统通知'
    },
    {
      key: 'loginNotifications' as const,
      title: '登录通知',
      description: '当有新设备登录时通知您',
      icon: '🔐',
      category: '安全通知'
    },
    {
      key: 'marketingEmails' as const,
      title: '营销邮件',
      description: '接收产品更新和优惠信息',
      icon: '📢',
      category: '营销通知'
    },
  ];

  const groupedNotifications = notificationItems.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, typeof notificationItems>);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>通知设置</CardTitle>
            {hasChanges && (
              <div className="flex space-x-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleReset}
                  disabled={isSaving}
                >
                  重置
                </Button>
                <Button 
                  size="sm" 
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? '保存中...' : '保存更改'}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {Object.entries(groupedNotifications).map(([category, items]) => (
              <div key={category}>
                <h3 className="text-sm font-medium text-gray-900 mb-3">{category}</h3>
                <div className="space-y-4">
                  {items.map((item) => (
                    <div key={item.key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">{item.icon}</span>
                        <div>
                          <h4 className="text-xs font-medium text-gray-900">{item.title}</h4>
                          <p className="text-xs text-gray-500 mt-1">{item.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge variant={formData[item.key] ? 'success' : 'secondary'}>
                          {formData[item.key] ? '开启' : '关闭'}
                        </Badge>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData[item.key]}
                            onChange={() => handleToggle(item.key)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 通知频率设置 */}
      <Card>
        <CardHeader>
          <CardTitle>通知频率</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-0.5">ℹ️</span>
                <div>
                  <h4 className="text-xs font-medium text-blue-900">智能通知</h4>
                  <p className="text-xs text-blue-700 mt-1">
                    我们会根据您的使用习惯智能调整通知频率，避免打扰您的正常使用。
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 border border-gray-200 rounded-lg">
                <h4 className="text-xs font-medium text-gray-900 mb-2">邮件摘要</h4>
                <p className="text-xs text-gray-600 mb-3">
                  将多个通知合并为每日或每周摘要
                </p>
                <select className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="immediate">立即发送</option>
                  <option value="daily">每日摘要</option>
                  <option value="weekly">每周摘要</option>
                  <option value="never">从不发送</option>
                </select>
              </div>

              <div className="p-3 border border-gray-200 rounded-lg">
                <h4 className="text-xs font-medium text-gray-900 mb-2">免打扰时间</h4>
                <p className="text-xs text-gray-600 mb-3">
                  设置不接收推送通知的时间段
                </p>
                <div className="flex space-x-2">
                  <input
                    type="time"
                    defaultValue="22:00"
                    className="flex-1 px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-500 self-center">至</span>
                  <input
                    type="time"
                    defaultValue="08:00"
                    className="flex-1 px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 通知历史 */}
      <Card>
        <CardHeader>
          <CardTitle>最近通知</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <span className="text-blue-500">🔔</span>
              <div className="flex-1">
                <p className="text-xs text-gray-900">欢迎使用 LoftChat</p>
                <p className="text-xs text-gray-500 mt-1">2小时前</p>
              </div>
              <Badge variant="secondary">系统</Badge>
            </div>
            
            <div className="text-center py-4">
              <p className="text-xs text-gray-500">暂无更多通知</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
