"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_controller_1 = require("../controllers/auth.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const validation_schemas_1 = require("../utils/validation.schemas");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.requestId);
router.use(auth_middleware_1.recordIP);
router.post('/register', (0, validation_middleware_1.validateJoiRequest)(validation_schemas_1.authValidation.register), auth_controller_1.AuthController.register);
router.post('/login', (0, validation_middleware_1.validateJoiRequest)(validation_schemas_1.authValidation.login), auth_controller_1.AuthController.login);
router.get('/me', auth_middleware_1.authenticateToken, auth_controller_1.AuthController.getCurrentUser);
router.post('/logout', auth_middleware_1.authenticateToken, auth_controller_1.AuthController.logout);
router.post('/refresh', auth_middleware_1.authenticateToken, auth_controller_1.AuthController.refreshToken);
router.post('/change-password', auth_middleware_1.authenticateToken, (0, validation_middleware_1.validateJoiRequest)(validation_schemas_1.authValidation.changePassword), auth_controller_1.AuthController.changePassword);
router.post('/forgot-password', (0, validation_middleware_1.validateJoiRequest)(validation_schemas_1.authValidation.forgotPassword), auth_controller_1.AuthController.forgotPassword);
router.post('/reset-password', (0, validation_middleware_1.validateJoiRequest)(validation_schemas_1.authValidation.resetPassword), auth_controller_1.AuthController.resetPassword);
router.get('/verify-email/:token', auth_controller_1.AuthController.verifyEmail);
router.post('/resend-verification', auth_middleware_1.authenticateToken, auth_controller_1.AuthController.resendVerification);
exports.default = router;
//# sourceMappingURL=auth.routes.js.map