import { EventEmitter } from 'events';
import * as os from 'os';
import * as fs from 'fs';
import { promisify } from 'util';
import { prisma } from '../config/database';
import { SocketService } from '../config/socket';
import type { SystemMonitoringData, SystemAlert } from '../config/socket';

const stat = promisify(fs.stat);

export interface SystemMetrics {
  // 系统性能指标
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  
  // 业务指标
  business: {
    totalUsers: number;
    activeUsers: number;
    onlineUsers: number;
    todayUploads: number;
    totalUploads: number;
    totalStorage: number;
  };
  
  // 系统状态
  system: {
    uptime: number;
    timestamp: string;
    serverInstance: string;
  };
}

export interface AlertThreshold {
  cpu: number;
  memory: number;
  disk: number;
  errorRate: number;
}

export class SystemMonitorService extends EventEmitter {
  private metricsInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private socketService: SocketService | null = null;
  private lastNetworkStats: any = null;

  // 默认告警阈值
  private alertThresholds: AlertThreshold = {
    cpu: 80,
    memory: 85,
    disk: 90,
    errorRate: 5
  };

  // 数据保留配置
  private dataRetentionConfig = {
    metricsRetentionHours: 24,    // 指标数据保留24小时
    eventsRetentionDays: 7,       // 事件日志保留7天
    cleanupIntervalMinutes: 60    // 每60分钟清理一次
  };

  constructor(socketService?: SocketService) {
    super();
    this.socketService = socketService || null;
    this.setupEventListeners();
  }

  /**
   * 启动系统监控
   */
  public startMonitoring(intervalMs: number = 30000): void {
    if (this.metricsInterval) {
      console.log('系统监控已在运行中');
      return;
    }

    // 启动指标收集任务
    this.metricsInterval = setInterval(async () => {
      try {
        await this.collectAndBroadcastMetrics();
      } catch (error) {
        console.error('收集系统指标失败:', error);
        await this.logSystemEvent('metric_collection_failed', 'error', (error as Error).message);
      }
    }, intervalMs);

    // 启动数据清理任务
    this.startDataCleanup();

    console.log(`系统监控已启动，采集间隔: ${intervalMs}ms`);
    console.log(`数据清理任务已启动，清理间隔: ${this.dataRetentionConfig.cleanupIntervalMinutes}分钟`);
    this.logSystemEvent('monitoring_started', 'info', '系统监控服务已启动');
  }

  /**
   * 停止系统监控
   */
  public stopMonitoring(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    console.log('系统监控已停止');
    this.logSystemEvent('monitoring_stopped', 'info', '系统监控服务已停止');
  }

  /**
   * 收集并广播系统指标
   */
  private async collectAndBroadcastMetrics(): Promise<void> {
    const metrics = await this.collectSystemMetrics();
    
    // 保存到数据库
    await this.saveMetricsToDatabase(metrics);
    
    // 检查告警条件
    this.checkAlerts(metrics);
    
    // 广播实时数据
    if (this.socketService) {
      const monitoringData: SystemMonitoringData = {
        cpu: metrics.cpu.usage,
        memory: metrics.memory.percentage,
        disk: metrics.disk.percentage,
        network: metrics.network,
        activeConnections: metrics.business.onlineUsers,
        timestamp: metrics.system.timestamp
      };
      
      this.socketService.broadcastSystemMonitoring(monitoringData);
    }
  }

  /**
   * 收集系统指标
   */
  public async collectSystemMetrics(): Promise<SystemMetrics> {
    const [cpuUsage, memoryUsage, diskUsage, networkStats, businessMetrics] = await Promise.all([
      this.getCPUUsage(),
      this.getMemoryUsage(),
      this.getDiskUsage(),
      this.getNetworkStats(),
      this.getBusinessMetrics()
    ]);

    return {
      cpu: cpuUsage,
      memory: memoryUsage,
      disk: diskUsage,
      network: networkStats,
      business: businessMetrics,
      system: {
        uptime: os.uptime(),
        timestamp: new Date().toISOString(),
        serverInstance: os.hostname()
      }
    };
  }

  /**
   * 获取CPU使用率
   */
  private async getCPUUsage(): Promise<SystemMetrics['cpu']> {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const usage = 100 - Math.floor(100 * idleDifference / totalDifference);
        
        resolve({
          usage: Math.max(0, Math.min(100, usage)),
          cores: os.cpus().length,
          loadAverage: os.loadavg()
        });
      }, 1000);
    });
  }

  /**
   * CPU平均值计算辅助函数
   */
  private cpuAverage(): { idle: number; total: number } {
    const cpus = os.cpus();
    let idle = 0;
    let total = 0;

    cpus.forEach(cpu => {
      Object.keys(cpu.times).forEach(type => {
        total += cpu.times[type as keyof typeof cpu.times];
      });
      idle += cpu.times.idle;
    });

    return { idle: idle / cpus.length, total: total / cpus.length };
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): SystemMetrics['memory'] {
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    return {
      total: Math.round(totalMem / 1024 / 1024), // MB
      used: Math.round(usedMem / 1024 / 1024), // MB
      free: Math.round(freeMem / 1024 / 1024), // MB
      percentage: Math.round((usedMem / totalMem) * 100)
    };
  }

  /**
   * 获取磁盘使用情况
   */
  private async getDiskUsage(): Promise<SystemMetrics['disk']> {
    try {
      // 在Windows上获取当前驱动器的磁盘使用情况
      const stats = await stat(process.cwd());
      
      // 这里使用模拟数据，实际项目中可以使用 diskusage 库
      const total = 100 * 1024 * 1024 * 1024; // 100GB 模拟
      const used = Math.floor(Math.random() * total * 0.8); // 随机使用量
      const free = total - used;
      
      return {
        total: Math.round(total / 1024 / 1024), // MB
        used: Math.round(used / 1024 / 1024), // MB
        free: Math.round(free / 1024 / 1024), // MB
        percentage: Math.round((used / total) * 100)
      };
    } catch (error) {
      console.error('获取磁盘使用情况失败:', error);
      return {
        total: 0,
        used: 0,
        free: 0,
        percentage: 0
      };
    }
  }

  /**
   * 获取网络统计
   */
  private getNetworkStats(): SystemMetrics['network'] {
    // 这里使用模拟数据，实际项目中可以使用系统命令或第三方库
    const currentStats = {
      bytesIn: Math.floor(Math.random() * 1000000),
      bytesOut: Math.floor(Math.random() * 1000000)
    };

    if (this.lastNetworkStats) {
      return {
        bytesIn: Math.max(0, currentStats.bytesIn - this.lastNetworkStats.bytesIn),
        bytesOut: Math.max(0, currentStats.bytesOut - this.lastNetworkStats.bytesOut)
      };
    }

    this.lastNetworkStats = currentStats;
    return { bytesIn: 0, bytesOut: 0 };
  }

  /**
   * 获取业务指标
   */
  private async getBusinessMetrics(): Promise<SystemMetrics['business']> {
    try {
      const [
        totalUsers,
        activeUsers,
        todayUploads,
        totalUploads,
        storageStats
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { status: 'active' } }),
        prisma.userImage.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        prisma.userImage.count(),
        prisma.image.aggregate({
          _sum: { fileSize: true },
          where: { isDeleted: false }
        })
      ]);

      // 模拟在线用户数（实际项目中应该从Redis或Socket连接中获取）
      const onlineUsers = Math.floor(Math.random() * activeUsers * 0.3);

      return {
        totalUsers,
        activeUsers,
        onlineUsers,
        todayUploads,
        totalUploads,
        totalStorage: Number(storageStats._sum.fileSize || 0)
      };
    } catch (error) {
      console.error('获取业务指标失败:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        onlineUsers: 0,
        todayUploads: 0,
        totalUploads: 0,
        totalStorage: 0
      };
    }
  }

  /**
   * 保存指标到数据库
   */
  private async saveMetricsToDatabase(metrics: SystemMetrics): Promise<void> {
    const serverInstance = metrics.system.serverInstance;
    
    try {
      await Promise.all([
        // 系统性能指标
        this.saveMetric('cpu_usage', metrics.cpu.usage, '%', serverInstance),
        this.saveMetric('memory_usage', metrics.memory.percentage, '%', serverInstance),
        this.saveMetric('disk_usage', metrics.disk.percentage, '%', serverInstance),
        this.saveMetric('network_in', metrics.network.bytesIn, 'bytes', serverInstance),
        this.saveMetric('network_out', metrics.network.bytesOut, 'bytes', serverInstance),
        
        // 业务指标
        this.saveMetric('total_users', metrics.business.totalUsers, 'count', serverInstance),
        this.saveMetric('active_users', metrics.business.activeUsers, 'count', serverInstance),
        this.saveMetric('online_users', metrics.business.onlineUsers, 'count', serverInstance),
        this.saveMetric('today_uploads', metrics.business.todayUploads, 'count', serverInstance),
        this.saveMetric('total_storage', metrics.business.totalStorage, 'bytes', serverInstance)
      ]);
    } catch (error) {
      console.error('保存指标到数据库失败:', error);
    }
  }

  /**
   * 保存单个指标
   */
  private async saveMetric(type: string, value: number, unit: string, serverInstance: string): Promise<void> {
    await prisma.systemMetric.create({
      data: {
        metricType: type,
        metricValue: value,
        unit,
        serverInstance
      }
    });
  }

  /**
   * 检查告警条件
   */
  private checkAlerts(metrics: SystemMetrics): void {
    const alerts: SystemAlert[] = [];

    // CPU告警检查
    if (metrics.cpu.usage > this.alertThresholds.cpu) {
      alerts.push({
        type: 'high_cpu',
        level: metrics.cpu.usage > 90 ? 'critical' : 'warning',
        message: `CPU使用率过高: ${metrics.cpu.usage}%`,
        value: metrics.cpu.usage,
        threshold: this.alertThresholds.cpu,
        timestamp: metrics.system.timestamp
      });
    }

    // 内存告警检查
    if (metrics.memory.percentage > this.alertThresholds.memory) {
      alerts.push({
        type: 'high_memory',
        level: metrics.memory.percentage > 95 ? 'critical' : 'warning',
        message: `内存使用率过高: ${metrics.memory.percentage}%`,
        value: metrics.memory.percentage,
        threshold: this.alertThresholds.memory,
        timestamp: metrics.system.timestamp
      });
    }

    // 磁盘告警检查
    if (metrics.disk.percentage > this.alertThresholds.disk) {
      alerts.push({
        type: 'high_disk',
        level: metrics.disk.percentage > 95 ? 'critical' : 'warning',
        message: `磁盘使用率过高: ${metrics.disk.percentage}%`,
        value: metrics.disk.percentage,
        threshold: this.alertThresholds.disk,
        timestamp: metrics.system.timestamp
      });
    }

    // 发送告警
    alerts.forEach(alert => {
      this.emitAlert(alert);
    });
  }

  /**
   * 发送告警
   */
  private emitAlert(alert: SystemAlert): void {
    // 触发事件
    this.emit('alert', alert);

    // 通过Socket广播告警
    if (this.socketService) {
      this.socketService.broadcastSystemAlert(alert);
    }

    // 记录告警日志
    this.logSystemEvent(
      `alert_${alert.type}`,
      alert.level === 'critical' ? 'critical' : 'warning',
      alert.message,
      { threshold: alert.threshold, value: alert.value }
    );

    console.warn(`[系统告警] ${alert.message}`);
  }

  /**
   * 设置告警阈值
   */
  public setAlertThresholds(thresholds: Partial<AlertThreshold>): void {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds };
    console.log('告警阈值已更新:', this.alertThresholds);
  }

  /**
   * 获取告警阈值
   */
  public getAlertThresholds(): AlertThreshold {
    return { ...this.alertThresholds };
  }

  /**
   * 设置数据保留策略
   */
  public setDataRetentionConfig(config: {
    metricsRetentionHours?: number;
    eventsRetentionDays?: number;
    cleanupIntervalMinutes?: number;
  }): void {
    this.dataRetentionConfig = { ...this.dataRetentionConfig, ...config };
    console.log('数据保留策略已更新:', this.dataRetentionConfig);

    // 如果清理任务正在运行，重启以应用新配置
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = setInterval(async () => {
        await this.performDataCleanup();
      }, this.dataRetentionConfig.cleanupIntervalMinutes * 60 * 1000);
    }
  }

  /**
   * 获取数据保留策略
   */
  public getDataRetentionConfig() {
    return { ...this.dataRetentionConfig };
  }

  /**
   * 记录系统事件
   */
  public async logSystemEvent(
    eventType: string,
    level: 'debug' | 'info' | 'warning' | 'error' | 'critical',
    message: string,
    data?: any
  ): Promise<void> {
    try {
      await prisma.systemEventLog.create({
        data: {
          eventType,
          eventLevel: level,
          eventMessage: message,
          eventData: data ? JSON.stringify(data) as any : null,
          serverInstance: os.hostname()
        }
      });
    } catch (error) {
      console.error('记录系统事件失败:', error);
    }
  }

  /**
   * 获取历史指标数据
   */
  public async getHistoricalMetrics(
    metricType: string,
    startTime: Date,
    endTime: Date,
    limit: number = 100
  ): Promise<any[]> {
    try {
      return await prisma.systemMetric.findMany({
        where: {
          metricType,
          createdAt: {
            gte: startTime,
            lte: endTime
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        select: {
          metricValue: true,
          unit: true,
          createdAt: true,
          serverInstance: true
        }
      });
    } catch (error) {
      console.error('获取历史指标数据失败:', error);
      return [];
    }
  }

  /**
   * 获取系统事件日志
   */
  public async getSystemEvents(
    eventLevel?: string,
    startTime?: Date,
    endTime?: Date,
    limit: number = 50
  ): Promise<any[]> {
    try {
      const where: any = {};

      if (eventLevel) {
        where.eventLevel = eventLevel;
      }

      if (startTime && endTime) {
        where.createdAt = {
          gte: startTime,
          lte: endTime
        };
      }

      return await prisma.systemEventLog.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        take: limit
      });
    } catch (error) {
      console.error('获取系统事件日志失败:', error);
      return [];
    }
  }

  /**
   * 启动数据清理任务
   */
  private startDataCleanup(): void {
    // 立即执行一次清理
    this.performDataCleanup();

    // 设置定时清理任务
    this.cleanupInterval = setInterval(async () => {
      await this.performDataCleanup();
    }, this.dataRetentionConfig.cleanupIntervalMinutes * 60 * 1000);
  }

  /**
   * 执行数据清理
   */
  private async performDataCleanup(): Promise<void> {
    try {
      console.log('开始执行数据清理任务...');

      // 清理过期的指标数据
      await this.cleanupMetricsData();

      // 清理过期的事件日志
      await this.cleanupEventLogs();

      console.log('数据清理任务完成');
      await this.logSystemEvent('data_cleanup_completed', 'info', '定期数据清理任务完成');
    } catch (error) {
      console.error('数据清理任务失败:', error);
      await this.logSystemEvent('data_cleanup_failed', 'error', (error as Error).message);
    }
  }

  /**
   * 清理过期的指标数据
   */
  private async cleanupMetricsData(): Promise<void> {
    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - this.dataRetentionConfig.metricsRetentionHours);

    const deletedMetrics = await prisma.systemMetric.deleteMany({
      where: {
        createdAt: {
          lt: cutoffTime
        }
      }
    });

    console.log(`清理了 ${deletedMetrics.count} 条过期指标数据（${this.dataRetentionConfig.metricsRetentionHours}小时前）`);
  }

  /**
   * 清理过期的事件日志
   */
  private async cleanupEventLogs(): Promise<void> {
    const cutoffTime = new Date();
    cutoffTime.setDate(cutoffTime.getDate() - this.dataRetentionConfig.eventsRetentionDays);

    const deletedEvents = await prisma.systemEventLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffTime
        }
      }
    });

    console.log(`清理了 ${deletedEvents.count} 条过期事件日志（${this.dataRetentionConfig.eventsRetentionDays}天前）`);
  }

  /**
   * 清理过期数据（手动调用）
   */
  public async cleanupOldData(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const [metricsDeleted, eventsDeleted] = await Promise.all([
        prisma.systemMetric.deleteMany({
          where: {
            createdAt: {
              lt: cutoffDate
            }
          }
        }),
        prisma.systemEventLog.deleteMany({
          where: {
            createdAt: {
              lt: cutoffDate
            }
          }
        })
      ]);

      console.log(`数据清理完成: 删除了 ${metricsDeleted.count} 条指标记录和 ${eventsDeleted.count} 条事件日志`);

      await this.logSystemEvent(
        'data_cleanup',
        'info',
        `清理了 ${daysToKeep} 天前的数据`,
        { metricsDeleted: metricsDeleted.count, eventsDeleted: eventsDeleted.count }
      );
    } catch (error) {
      console.error('清理过期数据失败:', error);
      await this.logSystemEvent('data_cleanup_failed', 'error', (error as Error).message);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听告警事件
    this.on('alert', (alert: SystemAlert) => {
      // 这里可以添加更多告警处理逻辑，如发送邮件、短信等
      console.log(`收到系统告警: ${alert.message}`);
    });

    // 监听进程退出事件，确保清理资源
    process.on('SIGINT', () => {
      this.stopMonitoring();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      this.stopMonitoring();
      process.exit(0);
    });
  }

  /**
   * 获取当前系统状态摘要
   */
  public async getSystemSummary(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    metrics: SystemMetrics;
    alerts: SystemAlert[];
  }> {
    const metrics = await this.collectSystemMetrics();
    const alerts: SystemAlert[] = [];

    // 检查各项指标状态
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (metrics.cpu.usage > this.alertThresholds.cpu ||
        metrics.memory.percentage > this.alertThresholds.memory ||
        metrics.disk.percentage > this.alertThresholds.disk) {
      status = 'warning';
    }

    if (metrics.cpu.usage > 90 ||
        metrics.memory.percentage > 95 ||
        metrics.disk.percentage > 95) {
      status = 'critical';
    }

    return {
      status,
      metrics,
      alerts
    };
  }
}
