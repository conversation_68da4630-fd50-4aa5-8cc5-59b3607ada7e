import { io, Socket } from 'socket.io-client';

// 日志数据类型定义
export interface SystemLogData {
  id: number;
  eventType: string;
  eventLevel: string;
  eventMessage: string;
  eventData?: any;
  serverInstance?: string;
  createdAt: string;
  type?: string;
}

export interface UploadLogData {
  id: number;
  userId: number;
  username?: string;
  imageId?: number;
  actionType: string;
  fileName?: string;
  fileSize?: number;
  ipAddress: string;
  userAgent?: string;
  isSuccess: boolean;
  errorMessage?: string;
  createdAt: string;
  type?: string;
}

export interface AccessLogData {
  id: number;
  imageId: number;
  ipAddress: string;
  userAgent?: string;
  referer?: string;
  country?: string;
  city?: string;
  responseTime?: number;
  createdAt: string;
  type?: string;
}

export interface AdminLogData {
  id: number;
  adminId: number;
  adminUsername?: string;
  operationType: string;
  targetType?: string;
  targetId?: number;
  operationDetails?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  type?: string;
}

export interface SystemMonitoringData {
  cpu: number;
  memory: number;
  disk: number;
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  activeConnections: number;
  timestamp: string;
}

export interface SystemAlert {
  type: 'high_cpu' | 'high_memory' | 'high_disk' | 'high_connections' | 'error_rate';
  level: 'warning' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: string;
}

export interface ConnectionStatus {
  connected: boolean;
  timestamp: string;
  clientsCount: number;
}

export interface LogFilters {
  types?: string[];
  levels?: string[];
  startDate?: string;
  endDate?: string;
  userId?: number;
  ipAddress?: string;
}

export interface LogHistoryParams {
  page: number;
  limit: number;
  filters?: LogFilters;
}

export type LogData = SystemLogData | UploadLogData | AccessLogData | AdminLogData;

// Socket.IO 客户端服务类
export class SocketService {
  private static instance: SocketService;
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  // 事件监听器
  private logListeners: ((log: LogData) => void)[] = [];
  private monitoringListeners: ((data: SystemMonitoringData) => void)[] = [];
  private alertListeners: ((alert: SystemAlert) => void)[] = [];
  private connectionListeners: ((status: ConnectionStatus) => void)[] = [];
  private errorListeners: ((error: any) => void)[] = [];

  private constructor() {}

  // 获取单例实例
  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  // 连接到 Socket.IO 服务器
  public connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const serverUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';
        
        this.socket = io(serverUrl, {
          auth: {
            token: token
          },
          transports: ['websocket', 'polling'],
          timeout: 20000,
          forceNew: true,
        });

        // 连接成功
        this.socket.on('connect', () => {
          console.log('Socket.IO 连接成功');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.setupEventListeners();
          resolve();
        });

        // 连接错误
        this.socket.on('connect_error', (error) => {
          console.error('Socket.IO 连接错误:', error);
          this.isConnected = false;
          
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            setTimeout(() => {
              this.socket?.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
          } else {
            reject(error);
          }
        });

        // 断开连接
        this.socket.on('disconnect', (reason) => {
          console.log('Socket.IO 连接断开:', reason);
          this.isConnected = false;
          
          // 自动重连（除非是手动断开）
          if (reason !== 'io client disconnect') {
            this.attemptReconnect();
          }
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  // 设置事件监听器
  private setupEventListeners(): void {
    if (!this.socket) return;

    // 日志事件
    this.socket.on('log:system', (log: SystemLogData) => {
      const logWithType = { ...log, type: 'system' };
      this.logListeners.forEach(listener => listener(logWithType));
    });

    this.socket.on('log:upload', (log: UploadLogData) => {
      const logWithType = { ...log, type: 'upload' };
      this.logListeners.forEach(listener => listener(logWithType));
    });

    this.socket.on('log:access', (log: AccessLogData) => {
      const logWithType = { ...log, type: 'access' };
      this.logListeners.forEach(listener => listener(logWithType));
    });

    this.socket.on('log:admin', (log: AdminLogData) => {
      const logWithType = { ...log, type: 'admin' };
      this.logListeners.forEach(listener => listener(logWithType));
    });

    // 监控事件
    this.socket.on('monitoring:system', (data: SystemMonitoringData) => {
      this.monitoringListeners.forEach(listener => listener(data));
    });

    this.socket.on('monitoring:alert', (alert: SystemAlert) => {
      this.alertListeners.forEach(listener => listener(alert));
    });

    // 连接状态事件
    this.socket.on('connection:status', (status: ConnectionStatus) => {
      this.connectionListeners.forEach(listener => listener(status));
    });

    // 错误事件
    this.socket.on('error', (error: any) => {
      console.error('Socket.IO 错误:', error);
      this.errorListeners.forEach(listener => listener(error));
    });
  }

  // 尝试重连
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.socket?.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  // 订阅日志
  public subscribeToLogs(filters?: LogFilters): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('logs:subscribe', filters || {});
    }
  }

  // 取消订阅日志
  public unsubscribeFromLogs(): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('logs:unsubscribe');
    }
  }

  // 获取历史日志
  public getHistoryLogs(params: LogHistoryParams): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this.socket && this.isConnected) {
        this.socket.emit('logs:history', params, (response: any) => {
          if (response.success) {
            resolve(response.data);
          } else {
            reject(new Error(response.error));
          }
        });
      } else {
        reject(new Error('Socket 未连接'));
      }
    });
  }

  // 订阅系统监控
  public subscribeToMonitoring(): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('monitoring:subscribe');
    }
  }

  // 取消订阅系统监控
  public unsubscribeFromMonitoring(): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('monitoring:unsubscribe');
    }
  }

  // 添加日志监听器
  public onLog(listener: (log: LogData) => void): () => void {
    this.logListeners.push(listener);
    return () => {
      const index = this.logListeners.indexOf(listener);
      if (index > -1) {
        this.logListeners.splice(index, 1);
      }
    };
  }

  // 添加监控数据监听器
  public onMonitoring(listener: (data: SystemMonitoringData) => void): () => void {
    this.monitoringListeners.push(listener);
    return () => {
      const index = this.monitoringListeners.indexOf(listener);
      if (index > -1) {
        this.monitoringListeners.splice(index, 1);
      }
    };
  }

  // 添加告警监听器
  public onAlert(listener: (alert: SystemAlert) => void): () => void {
    this.alertListeners.push(listener);
    return () => {
      const index = this.alertListeners.indexOf(listener);
      if (index > -1) {
        this.alertListeners.splice(index, 1);
      }
    };
  }

  // 添加连接状态监听器
  public onConnectionStatus(listener: (status: ConnectionStatus) => void): () => void {
    this.connectionListeners.push(listener);
    return () => {
      const index = this.connectionListeners.indexOf(listener);
      if (index > -1) {
        this.connectionListeners.splice(index, 1);
      }
    };
  }

  // 添加错误监听器
  public onError(listener: (error: any) => void): () => void {
    this.errorListeners.push(listener);
    return () => {
      const index = this.errorListeners.indexOf(listener);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  // 断开连接
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.reconnectAttempts = 0;
    }
  }

  // 获取连接状态
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }
}
