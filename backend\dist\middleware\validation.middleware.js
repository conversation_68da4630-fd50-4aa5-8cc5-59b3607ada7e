"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRequest = exports.validatePagination = exports.validateFileUpload = exports.validateParams = exports.validateQuery = exports.validateJoiRequest = void 0;
const express_validator_1 = require("express-validator");
const joi_1 = __importDefault(require("joi"));
const types_1 = require("../types");
const validateJoiRequest = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true,
            convert: true,
        });
        if (error) {
            const errorDetails = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
                value: detail.context?.value,
            }));
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_INPUT,
                    message: '请求数据验证失败',
                    details: errorDetails,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        req.body = value;
        next();
    };
};
exports.validateJoiRequest = validateJoiRequest;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.query, {
            abortEarly: false,
            stripUnknown: true,
            convert: true,
        });
        if (error) {
            const errorDetails = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
                value: detail.context?.value,
            }));
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_INPUT,
                    message: '查询参数验证失败',
                    details: errorDetails,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        req.query = value;
        next();
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.params, {
            abortEarly: false,
            stripUnknown: true,
            convert: true,
        });
        if (error) {
            const errorDetails = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
                value: detail.context?.value,
            }));
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_INPUT,
                    message: '路径参数验证失败',
                    details: errorDetails,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        req.params = value;
        next();
    };
};
exports.validateParams = validateParams;
const validateFileUpload = (options) => {
    return (req, res, next) => {
        const file = req.file;
        const { maxSize, allowedMimeTypes, required = true } = options;
        if (required && !file) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_INPUT,
                    message: '文件不能为空',
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        if (!file) {
            next();
            return;
        }
        if (maxSize && file.size > maxSize) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.FILE_TOO_LARGE,
                    message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        if (allowedMimeTypes && !allowedMimeTypes.includes(file.mimetype)) {
            res.status(400).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_FILE_TYPE,
                    message: `不支持的文件类型，仅支持: ${allowedMimeTypes.join(', ')}`,
                    timestamp: new Date().toISOString(),
                    requestId: req.requestId,
                }
            });
            return;
        }
        next();
    };
};
exports.validateFileUpload = validateFileUpload;
const validatePagination = (req, res, next) => {
    const paginationSchema = joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20),
        sortBy: joi_1.default.string().optional(),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
    });
    const { error, value } = paginationSchema.validate(req.query, {
        stripUnknown: false,
        convert: true,
    });
    if (error) {
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INVALID_INPUT,
                message: '分页参数验证失败',
                details: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                })),
                timestamp: new Date().toISOString(),
                requestId: req.requestId,
            }
        });
        return;
    }
    req.pagination = {
        page: value.page,
        limit: value.limit,
        offset: (value.page - 1) * value.limit,
        sortBy: value.sortBy,
        sortOrder: value.sortOrder,
    };
    next();
};
exports.validatePagination = validatePagination;
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const errorDetails = errors.array().map(error => ({
            field: error.type === 'field' ? error.path : error.type,
            message: error.msg,
            value: error.type === 'field' ? error.value : undefined,
        }));
        res.status(400).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.VALIDATION_ERROR,
                message: '请求数据验证失败',
                details: errorDetails,
                timestamp: new Date().toISOString(),
            }
        });
        return;
    }
    next();
};
exports.validateRequest = validateRequest;
//# sourceMappingURL=validation.middleware.js.map