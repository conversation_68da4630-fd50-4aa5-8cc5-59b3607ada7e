import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { adminService } from '../../services/admin.service';
import { socketService } from '../../services/socket.service';
import type { SystemMonitoringData, SystemAlert } from '../../services/socket.service';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  business: {
    totalUsers: number;
    activeUsers: number;
    onlineUsers: number;
    todayUploads: number;
    totalUploads: number;
    totalStorage: number;
  };
  system: {
    uptime: number;
    timestamp: string;
    serverInstance: string;
  };
}

interface SystemSummary {
  status: 'healthy' | 'warning' | 'critical';
  metrics: SystemMetrics;
  alerts: SystemAlert[];
}

interface MonitoringData {
  summary: SystemSummary;
  events: any[];
  charts: {
    cpu: Array<{ timestamp: string; value: number; unit: string }>;
    memory: Array<{ timestamp: string; value: number; unit: string }>;
  };
  thresholds: {
    cpu: number;
    memory: number;
    disk: number;
    errorRate: number;
  };
}

export function SystemMonitoring() {
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null);
  const [realtimeData, setRealtimeData] = useState<SystemMonitoringData | null>(null);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    loadMonitoringData();
    setupRealtimeConnection();

    // 设置定时刷新
    const interval = setInterval(loadMonitoringData, 60000); // 每分钟刷新一次完整数据

    return () => {
      clearInterval(interval);
      cleanupRealtimeConnection();
    };
  }, []);

  const loadMonitoringData = async () => {
    try {
      const response = await fetch('/api/admin/monitoring/system', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setMonitoringData(result.data);
        }
      }
    } catch (error) {
      console.error('加载监控数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeConnection = () => {
    // 连接Socket.IO
    socketService.connect();

    // 监听连接状态
    socketService.onConnectionChange((status) => {
      setConnected(status.connected);
    });

    // 订阅系统监控数据
    socketService.subscribeToMonitoring();

    // 监听实时监控数据
    socketService.onSystemMonitoring((data: SystemMonitoringData) => {
      setRealtimeData(data);
    });

    // 监听系统告警
    socketService.onSystemAlert((alert: SystemAlert) => {
      setAlerts(prev => [alert, ...prev.slice(0, 9)]); // 保留最近10条告警
    });
  };

  const cleanupRealtimeConnection = () => {
    socketService.unsubscribeFromMonitoring();
    socketService.disconnect();
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'healthy': return '健康';
      case 'warning': return '警告';
      case 'critical': return '严重';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
            <p className="text-gray-600">加载监控数据中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!monitoringData) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              <p>无法加载监控数据</p>
              <button
                onClick={loadMonitoringData}
                className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                重试
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentMetrics = realtimeData || {
    cpu: monitoringData.summary.metrics.cpu.usage,
    memory: monitoringData.summary.metrics.memory.percentage,
    disk: monitoringData.summary.metrics.disk.percentage,
    network: monitoringData.summary.metrics.network,
    activeConnections: monitoringData.summary.metrics.business.onlineUsers,
    timestamp: monitoringData.summary.metrics.system.timestamp
  };

  return (
    <div className="space-y-6">
      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 mb-1">系统状态</p>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(monitoringData.summary.status)}>
                    {getStatusText(monitoringData.summary.status)}
                  </Badge>
                  <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-400' : 'bg-red-400'}`} />
                </div>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500">运行时间</p>
                <p className="text-xs font-medium">
                  {formatUptime(monitoringData.summary.metrics.system.uptime)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 mb-1">CPU使用率</p>
                <p className="text-lg font-semibold">{currentMetrics.cpu}%</p>
              </div>
              <div className="w-12 h-12 relative">
                <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#e5e7eb"
                    strokeWidth="2"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke={currentMetrics.cpu > 80 ? "#ef4444" : currentMetrics.cpu > 60 ? "#f59e0b" : "#10b981"}
                    strokeWidth="2"
                    strokeDasharray={`${currentMetrics.cpu}, 100`}
                  />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 mb-1">内存使用率</p>
                <p className="text-lg font-semibold">{currentMetrics.memory}%</p>
                <p className="text-xs text-gray-500">
                  {formatBytes(monitoringData.summary.metrics.memory.used * 1024 * 1024)} /
                  {formatBytes(monitoringData.summary.metrics.memory.total * 1024 * 1024)}
                </p>
              </div>
              <div className="w-12 h-12 relative">
                <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#e5e7eb"
                    strokeWidth="2"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke={currentMetrics.memory > 85 ? "#ef4444" : currentMetrics.memory > 70 ? "#f59e0b" : "#3b82f6"}
                    strokeWidth="2"
                    strokeDasharray={`${currentMetrics.memory}, 100`}
                  />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 mb-1">磁盘使用率</p>
                <p className="text-lg font-semibold">{currentMetrics.disk}%</p>
                <p className="text-xs text-gray-500">
                  {formatBytes(monitoringData.summary.metrics.disk.used * 1024 * 1024)} /
                  {formatBytes(monitoringData.summary.metrics.disk.total * 1024 * 1024)}
                </p>
              </div>
              <div className="w-12 h-12 relative">
                <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#e5e7eb"
                    strokeWidth="2"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke={currentMetrics.disk > 90 ? "#ef4444" : currentMetrics.disk > 75 ? "#f59e0b" : "#8b5cf6"}
                    strokeWidth="2"
                    strokeDasharray={`${currentMetrics.disk}, 100`}
                  />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 业务指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">总用户数</p>
              <p className="text-2xl font-bold text-blue-600">
                {monitoringData.summary.metrics.business.totalUsers.toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">活跃用户</p>
              <p className="text-2xl font-bold text-green-600">
                {monitoringData.summary.metrics.business.activeUsers.toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">在线用户</p>
              <p className="text-2xl font-bold text-purple-600">
                {currentMetrics.activeConnections.toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">今日上传</p>
              <p className="text-2xl font-bold text-orange-600">
                {monitoringData.summary.metrics.business.todayUploads.toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 性能图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">CPU使用率趋势</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={monitoringData.charts.cpu.slice(-20)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    fontSize={10}
                  />
                  <YAxis domain={[0, 100]} fontSize={10} />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number) => [`${value}%`, 'CPU使用率']}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#10b981"
                    fill="#10b981"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">内存使用率趋势</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={monitoringData.charts.memory.slice(-20)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    fontSize={10}
                  />
                  <YAxis domain={[0, 100]} fontSize={10} />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number) => [`${value}%`, '内存使用率']}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 告警信息 */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm flex items-center">
              <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
              系统告警
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {alerts.slice(0, 5).map((alert, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border-l-4 ${
                    alert.level === 'critical'
                      ? 'bg-red-50 border-red-400'
                      : 'bg-yellow-50 border-yellow-400'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium ${
                        alert.level === 'critical' ? 'text-red-800' : 'text-yellow-800'
                      }`}>
                        {alert.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <Badge className={
                      alert.level === 'critical'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }>
                      {alert.level === 'critical' ? '严重' : '警告'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 系统事件日志 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">最近系统事件</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {monitoringData.events.slice(0, 10).map((event, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    event.eventLevel === 'error' || event.eventLevel === 'critical' ? 'bg-red-400' :
                    event.eventLevel === 'warning' ? 'bg-yellow-400' :
                    'bg-green-400'
                  }`} />
                  <div>
                    <p className="text-sm font-medium">{event.eventMessage}</p>
                    <p className="text-xs text-gray-500">{event.eventType}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-500">
                    {new Date(event.createdAt).toLocaleString()}
                  </p>
                  <Badge className={`text-xs ${
                    event.eventLevel === 'error' || event.eventLevel === 'critical' ? 'bg-red-100 text-red-800' :
                    event.eventLevel === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {event.eventLevel}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 网络流量 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">网络流量</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">入站流量</p>
              <p className="text-lg font-semibold text-blue-600">
                {formatBytes(currentMetrics.network.bytesIn)}/s
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">出站流量</p>
              <p className="text-lg font-semibold text-green-600">
                {formatBytes(currentMetrics.network.bytesOut)}/s
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
