const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function restoreData() {
  try {
    console.log('🔄 开始恢复数据...');

    // 1. 恢复用户等级配置
    console.log('📊 恢复用户等级配置...');
    const levelConfigs = [
      {
        level: 'free',
        displayName: '免费用户',
        maxDailyUploads: 10,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        maxStorageSpace: 1024 * 1024 * 1024, // 1GB
        canChooseProvider: false,
        prioritySupport: false,
        customDomain: false,
        visibleProviderCount: 1
      },
      {
        level: 'vip1',
        displayName: 'VIP 1',
        maxDailyUploads: 50,
        maxFileSize: 20 * 1024 * 1024, // 20MB
        maxStorageSpace: 10 * 1024 * 1024 * 1024, // 10GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: false,
        visibleProviderCount: 2
      },
      {
        level: 'vip2',
        displayName: 'VIP 2',
        maxDailyUploads: 200,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        maxStorageSpace: 50 * 1024 * 1024 * 1024, // 50GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 3
      },
      {
        level: 'vip3',
        displayName: 'VIP 3',
        maxDailyUploads: 1000,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        maxStorageSpace: 200 * 1024 * 1024 * 1024, // 200GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 4
      }
    ];

    for (const config of levelConfigs) {
      await prisma.userLevelConfig.upsert({
        where: { level: config.level },
        update: config,
        create: config
      });
      console.log(`✅ 已恢复等级配置: ${config.displayName}`);
    }

    // 2. 恢复图片提供商配置
    console.log('🖼️ 恢复图片提供商配置...');
    const providers = [
      {
        name: 'local',
        description: '本地文件系统存储',
        endpoint: 'http://localhost:3000/uploads',
        apiKey: null,
        isEnabled: true,
        priority: 1,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        allowedTypes: 'jpg,jpeg,png,gif,webp,bmp',
        config: {
          baseUrl: 'http://localhost:3000/uploads',
          uploadPath: './uploads'
        }
      },
      {
        name: 'cloudinary',
        description: '专业的云端图片和视频管理服务',
        endpoint: 'https://api.cloudinary.com/v1_1',
        apiKey: null,
        isEnabled: false,
        priority: 2,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedTypes: 'jpg,jpeg,png,gif,webp,bmp',
        config: {
          cloudName: '',
          apiSecret: '',
          folder: 'loftchat'
        }
      },
      {
        name: 'imgur',
        description: '流行的图片分享平台',
        endpoint: 'https://api.imgur.com/3',
        apiKey: null,
        isEnabled: false,
        priority: 3,
        maxFileSize: 20 * 1024 * 1024, // 20MB
        allowedTypes: 'jpg,jpeg,png,gif,webp',
        config: {
          clientSecret: '',
          albumId: ''
        }
      },
      {
        name: 'github',
        description: '使用 GitHub 仓库作为图片存储',
        endpoint: 'https://api.github.com',
        apiKey: null,
        isEnabled: false,
        priority: 4,
        maxFileSize: 25 * 1024 * 1024, // 25MB
        allowedTypes: 'jpg,jpeg,png,gif,webp,bmp',
        config: {
          owner: '',
          repo: '',
          branch: 'main',
          path: 'images'
        }
      }
    ];

    for (const provider of providers) {
      await prisma.uploadProvider.upsert({
        where: { name: provider.name },
        update: provider,
        create: provider
      });
      console.log(`✅ 已恢复提供商配置: ${provider.name}`);
    }

    // 3. 创建默认管理员账户
    console.log('👤 创建默认管理员账户...');
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456';
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    const adminUser = await prisma.user.upsert({
      where: { email: adminEmail },
      update: {},
      create: {
        username: 'admin',
        email: adminEmail,
        passwordHash: hashedPassword,
        role: 'admin',
        userLevel: 'vip3',
        status: 'active',
        displayName: '系统管理员',
        bio: '系统默认管理员账户',
        profileVisibility: 'public',
        allowDirectMessages: true,
        showOnlineStatus: true,
        emailNotifications: true,
        pushNotifications: true,
        marketingEmails: false,
        defaultImageQuality: 85,
        autoCompress: true,
        defaultImageFormat: 'webp',
        maxImageSize: 50 * 1024 * 1024, // 50MB
        twoFactorEnabled: false,
        loginNotifications: true,
        levelExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1年后过期
      }
    });

    console.log(`✅ 已创建管理员账户: ${adminEmail} / ${adminPassword}`);

    // 4. 创建测试用户账户
    console.log('👥 创建测试用户账户...');
    const testUsers = [
      {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'test123456',
        role: 'user',
        userLevel: 'free',
        displayName: '测试用户',
        bio: '这是一个测试用户账户'
      },
      {
        username: 'vipuser',
        email: '<EMAIL>',
        password: 'vip123456',
        role: 'user',
        userLevel: 'vip1',
        displayName: 'VIP用户',
        bio: '这是一个VIP测试用户账户'
      }
    ];

    for (const userData of testUsers) {
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      
      const user = await prisma.user.upsert({
        where: { email: userData.email },
        update: {},
        create: {
          username: userData.username,
          email: userData.email,
          passwordHash: hashedPassword,
          role: userData.role,
          userLevel: userData.userLevel,
          status: 'active',
          displayName: userData.displayName,
          bio: userData.bio,
          profileVisibility: 'public',
          allowDirectMessages: true,
          showOnlineStatus: true,
          emailNotifications: true,
          pushNotifications: true,
          marketingEmails: false,
          defaultImageQuality: 80,
          autoCompress: true,
          defaultImageFormat: 'original',
          maxImageSize: 10 * 1024 * 1024, // 10MB
          twoFactorEnabled: false,
          loginNotifications: true,
          levelExpiresAt: userData.userLevel !== 'free' 
            ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
            : null
        }
      });

      console.log(`✅ 已创建测试用户: ${userData.email} / ${userData.password}`);
    }

    // 5. 创建系统配置
    console.log('⚙️ 创建系统配置...');
    const systemConfigs = [
      {
        key: 'site_name',
        value: 'LoftChat',
        description: '网站名称'
      },
      {
        key: 'site_description',
        value: '专业的图片托管和分享平台',
        description: '网站描述'
      },
      {
        key: 'max_upload_size',
        value: '100MB',
        description: '最大上传文件大小'
      },
      {
        key: 'allowed_file_types',
        value: 'jpg,jpeg,png,gif,webp,bmp',
        description: '允许的文件类型'
      },
      {
        key: 'enable_registration',
        value: true,
        description: '是否允许用户注册'
      },
      {
        key: 'enable_guest_upload',
        value: false,
        description: '是否允许游客上传'
      }
    ];

    for (const config of systemConfigs) {
      await prisma.systemConfig.upsert({
        where: { key: config.key },
        update: { value: config.value, description: config.description },
        create: config
      });
      console.log(`✅ 已创建系统配置: ${config.key} = ${JSON.stringify(config.value)}`);
    }

    console.log('\n🎉 数据恢复完成！');
    console.log('\n📋 账户信息:');
    console.log('管理员账户: <EMAIL> / admin123456');
    console.log('测试用户: <EMAIL> / test123456');
    console.log('VIP用户: <EMAIL> / vip123456');
    console.log('\n🚀 现在可以使用这些账户登录系统了！');

  } catch (error) {
    console.error('❌ 数据恢复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行恢复脚本
restoreData();
