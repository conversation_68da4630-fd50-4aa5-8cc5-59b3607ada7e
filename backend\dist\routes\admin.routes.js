"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_middleware_1 = require("../middleware/auth.middleware");
const admin_controller_1 = require("../controllers/admin.controller");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authenticateToken);
router.use(auth_middleware_1.requireAdmin);
router.get('/stats', admin_controller_1.AdminController.getSystemStats);
router.get('/users', admin_controller_1.AdminController.getUsers);
router.put('/users/:id/level', admin_controller_1.AdminController.updateUserLevel);
router.put('/users/:userId/status', admin_controller_1.AdminController.updateUserStatus);
router.put('/users/:userId/profile', admin_controller_1.AdminController.updateUserProfile);
router.get('/users/:userId/activity-logs', admin_controller_1.AdminController.getUserActivityLogs);
router.get('/users/:userId/login-history', admin_controller_1.AdminController.getUserLoginHistory);
router.get('/level-applications', admin_controller_1.AdminController.getLevelApplications);
router.put('/level-applications/:applicationId/process', admin_controller_1.AdminController.processLevelApplication);
router.get('/users/:userId', admin_controller_1.AdminController.getUserDetail);
router.get('/logs/realtime', admin_controller_1.AdminController.getRealtimeLogs);
router.get('/logs/system', admin_controller_1.AdminController.getSystemLogs);
router.get('/providers', admin_controller_1.AdminController.getProviders);
router.post('/providers', admin_controller_1.AdminController.createProvider);
router.put('/providers/:id', admin_controller_1.AdminController.updateProvider);
router.delete('/providers/:id', admin_controller_1.AdminController.deleteProvider);
router.get('/monitoring/system', admin_controller_1.AdminController.getSystemMonitoring);
router.get('/monitoring/metrics/:type', admin_controller_1.AdminController.getMetricsHistory);
router.get('/monitoring/events', admin_controller_1.AdminController.getSystemEvents);
router.put('/monitoring/thresholds', admin_controller_1.AdminController.updateAlertThresholds);
router.post('/monitoring/cleanup', admin_controller_1.AdminController.cleanupMonitoringData);
router.get('/security/ips', admin_controller_1.AdminController.getIPSecurity);
router.post('/security/ips/blacklist', admin_controller_1.AdminController.addIPToBlacklist);
router.delete('/security/ips/blacklist/:ip', admin_controller_1.AdminController.removeIPFromBlacklist);
router.get('/analytics/overview', admin_controller_1.AdminController.getAnalyticsOverview);
router.get('/analytics/users', admin_controller_1.AdminController.getUserAnalytics);
router.get('/analytics/uploads', admin_controller_1.AdminController.getUploadAnalytics);
exports.default = router;
//# sourceMappingURL=admin.routes.js.map