"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authenticateToken);
router.get('/profile', user_controller_1.UserController.getProfile);
router.put('/profile', [
    (0, express_validator_1.body)('displayName')
        .optional()
        .isLength({ max: 100 })
        .withMessage('显示名称不能超过100个字符'),
    (0, express_validator_1.body)('bio')
        .optional()
        .isLength({ max: 500 })
        .withMessage('个人简介不能超过500个字符'),
    (0, express_validator_1.body)('location')
        .optional()
        .isLength({ max: 100 })
        .withMessage('位置不能超过100个字符'),
    (0, express_validator_1.body)('website')
        .optional()
        .isURL()
        .withMessage('请输入有效的网站URL'),
    (0, express_validator_1.body)('profileVisibility')
        .optional()
        .isIn(['public', 'private', 'friends'])
        .withMessage('无效的隐私设置'),
    (0, express_validator_1.body)('allowDirectMessages')
        .optional()
        .isBoolean()
        .withMessage('允许私信必须是布尔值'),
    (0, express_validator_1.body)('showOnlineStatus')
        .optional()
        .isBoolean()
        .withMessage('显示在线状态必须是布尔值'),
    validation_middleware_1.validateRequest
], user_controller_1.UserController.updateProfile);
router.put('/password', [
    (0, express_validator_1.body)('currentPassword')
        .notEmpty()
        .withMessage('当前密码是必需的'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 6 })
        .withMessage('新密码长度至少为6位')
        .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
        .withMessage('新密码必须包含字母和数字'),
    validation_middleware_1.validateRequest
], user_controller_1.UserController.changePassword);
router.put('/notifications', [
    (0, express_validator_1.body)('emailNotifications')
        .optional()
        .isBoolean()
        .withMessage('邮件通知必须是布尔值'),
    (0, express_validator_1.body)('pushNotifications')
        .optional()
        .isBoolean()
        .withMessage('推送通知必须是布尔值'),
    (0, express_validator_1.body)('marketingEmails')
        .optional()
        .isBoolean()
        .withMessage('营销邮件必须是布尔值'),
    (0, express_validator_1.body)('loginNotifications')
        .optional()
        .isBoolean()
        .withMessage('登录通知必须是布尔值'),
    validation_middleware_1.validateRequest
], user_controller_1.UserController.updateNotificationSettings);
router.put('/upload-settings', [
    (0, express_validator_1.body)('defaultImageQuality')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('图片质量必须在1-100之间'),
    (0, express_validator_1.body)('autoCompress')
        .optional()
        .isBoolean()
        .withMessage('自动压缩必须是布尔值'),
    (0, express_validator_1.body)('defaultImageFormat')
        .optional()
        .isIn(['original', 'webp', 'jpeg', 'png'])
        .withMessage('无效的图片格式'),
    (0, express_validator_1.body)('maxImageSize')
        .optional()
        .isInt({ min: 1024, max: 50 * 1024 * 1024 })
        .withMessage('最大图片大小必须在1KB-50MB之间'),
    validation_middleware_1.validateRequest
], user_controller_1.UserController.updateUploadSettings);
router.get('/activity-logs', [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('每页数量必须在1-100之间'),
    (0, express_validator_1.query)('activityType')
        .optional()
        .isString()
        .withMessage('活动类型必须是字符串'),
    validation_middleware_1.validateRequest
], user_controller_1.UserController.getActivityLogs);
router.get('/login-history', [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('每页数量必须在1-100之间'),
    validation_middleware_1.validateRequest
], user_controller_1.UserController.getLoginHistory);
exports.default = router;
//# sourceMappingURL=user.routes.js.map