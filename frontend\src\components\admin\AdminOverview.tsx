import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { adminService } from '../../services/admin.service';
import type { SystemStats } from '../../services/admin.service';



interface RealtimeLog {
  id: string;
  timestamp?: string;
  createdAt?: string;
  type: 'upload' | 'login' | 'register' | 'error' | 'warning' | 'system';
  user?: {
    username: string;
    level: string;
    ip: string;
  };
  // 上传日志字段
  username?: string;
  actionType?: string;
  ipAddress?: string;
  isSuccess?: boolean;
  // 系统日志字段
  eventType?: string;
  eventLevel?: string;
  eventMessage?: string;
  // 通用字段
  action?: string;
  details?: string;
  status?: 'success' | 'failed' | 'warning';
}

export function AdminOverview() {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [realtimeLogs, setRealtimeLogs] = useState<RealtimeLog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSystemStats();
    loadRealtimeLogs();
    
    // 设置定时刷新
    const interval = setInterval(() => {
      loadSystemStats();
      loadRealtimeLogs();
    }, 5000); // 每5秒刷新一次

    return () => clearInterval(interval);
  }, []);

  const loadSystemStats = async () => {
    try {
      const stats = await adminService.getSystemStats();
      setStats(stats);
    } catch (error) {
      console.error('加载系统统计失败:', error);
      // 如果API失败，显示错误状态
      setStats(null);
    }
  };

  const loadRealtimeLogs = async () => {
    try {
      const logs = await adminService.getRealtimeLogs();
      setRealtimeLogs(logs);
      setLoading(false);
    } catch (error) {
      console.error('加载实时日志失败:', error);
      // 如果API失败，使用空数组
      setRealtimeLogs([]);
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getLogTypeColor = (type: string) => {
    switch (type) {
      case 'upload': return 'bg-blue-100 text-blue-800';
      case 'login': return 'bg-green-100 text-green-800';
      case 'register': return 'bg-purple-100 text-purple-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'system': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      case 'critical': return 'text-red-700';
      case 'info': return 'text-blue-600';
      case 'debug': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 系统统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">总用户数</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalUsers.toLocaleString()}</p>
                <p className="text-xs text-gray-500">活跃用户 {stats?.activeUsers}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">总上传数</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalUploads.toLocaleString()}</p>
                <p className="text-xs text-gray-500">今日 {stats?.todayUploads}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">存储使用</p>
                <p className="text-2xl font-bold text-gray-900">{formatFileSize(stats?.totalStorage || 0)}</p>
                <p className="text-xs text-gray-500">系统总存储</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">在线用户</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.onlineUsers}</p>
                <p className="text-xs text-gray-500">实时在线</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 系统负载监控 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>CPU 使用率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${stats?.systemLoad.cpu}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-sm font-medium">{stats?.systemLoad.cpu}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>内存使用率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${stats?.systemLoad.memory}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-sm font-medium">{stats?.systemLoad.memory}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>磁盘使用率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${stats?.systemLoad.disk}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-sm font-medium">{stats?.systemLoad.disk}%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 实时日志 */}
      <Card>
        <CardHeader>
          <CardTitle>实时系统日志</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {realtimeLogs.map((log) => (
              <div key={log.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <Badge className={getLogTypeColor(log.type)}>
                    {log.type}
                  </Badge>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    {log.user ? (
                      <>
                        <span className="font-medium text-gray-900">{log.user.username}</span>
                        <Badge variant="secondary">{log.user.level}</Badge>
                        <span className="text-sm text-gray-500">{log.user.ip}</span>
                      </>
                    ) : (
                      <>
                        <span className="font-medium text-gray-900">{log.username || '系统'}</span>
                        <Badge variant="secondary">系统</Badge>
                        <span className="text-sm text-gray-500">{log.ipAddress || 'N/A'}</span>
                      </>
                    )}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {log.action || log.eventMessage || log.actionType} - {log.details || log.eventType || '系统日志'}
                  </div>
                </div>
                <div className="flex-shrink-0 text-right">
                  <div className={`text-sm font-medium ${getStatusColor(log.status || (log.isSuccess !== undefined ? (log.isSuccess ? 'success' : 'failed') : 'info'))}`}>
                    {log.status || (log.isSuccess !== undefined ? (log.isSuccess ? 'success' : 'failed') : log.eventLevel || 'info')}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(log.timestamp || log.createdAt || new Date()).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
