import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  displayName?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatarUrl?: string;
  role: string;
  userLevel: string;
  status: string;
  profileVisibility: string;
  allowDirectMessages: boolean;
  showOnlineStatus: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  defaultImageQuality: number;
  autoCompress: boolean;
  defaultImageFormat: string;
  maxImageSize: number;
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  lastActiveAt?: string;
  levelExpiresAt?: string;
  _count?: {
    userImages: number;
    uploadLogs: number;
    activityLogs: number;
    loginHistory: number;
  };
}

interface ActivityLog {
  id: number;
  activityType: string;
  activityData?: any;
  ipAddress?: string;
  location?: string;
  deviceInfo?: any;
  createdAt: string;
}

interface LoginHistory {
  id: number;
  loginType: string;
  isSuccess: boolean;
  failureReason?: string;
  ipAddress: string;
  location?: string;
  deviceInfo?: any;
  createdAt: string;
}

interface EnhancedUserDetailModalProps {
  userId: number | null;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdated?: (user: UserProfile) => void;
}

type TabType = 'profile' | 'activity' | 'login' | 'settings' | 'security';

export function EnhancedUserDetailModal({ userId, isOpen, onClose, onUserUpdated }: EnhancedUserDetailModalProps) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [loginHistory, setLoginHistory] = useState<LoginHistory[]>([]);
  const [activityLoading, setActivityLoading] = useState(false);
  const [loginLoading, setLoginLoading] = useState(false);

  const [formData, setFormData] = useState({
    displayName: '',
    bio: '',
    location: '',
    website: '',
    userLevel: '',
    status: '',
    profileVisibility: '',
    allowDirectMessages: false,
    showOnlineStatus: false,
    emailNotifications: false,
    pushNotifications: false,
    marketingEmails: false,
    defaultImageQuality: 80,
    autoCompress: true,
    defaultImageFormat: 'original',
    maxImageSize: 10485760,
    twoFactorEnabled: false,
    loginNotifications: false,
  });

  const tabs = [
    { id: 'profile' as const, label: '基本信息', icon: '👤' },
    { id: 'activity' as const, label: '活动记录', icon: '📊' },
    { id: 'login' as const, label: '登录历史', icon: '🔐' },
    { id: 'settings' as const, label: '用户设置', icon: '⚙️' },
    { id: 'security' as const, label: '安全信息', icon: '🛡️' },
  ];

  useEffect(() => {
    if (isOpen && userId) {
      loadUserDetails();
      setActiveTab('profile');
    }
  }, [isOpen, userId]);

  useEffect(() => {
    if (user && activeTab === 'activity' && activityLogs.length === 0) {
      loadActivityLogs();
    }
    if (user && activeTab === 'login' && loginHistory.length === 0) {
      loadLoginHistory();
    }
  }, [activeTab, user]);

  const loadUserDetails = async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch(`/api/admin/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取用户详情失败');
      }

      const data = await response.json();
      if (data.success) {
        setUser(data.data);
        setFormData({
          displayName: data.data.displayName || '',
          bio: data.data.bio || '',
          location: data.data.location || '',
          website: data.data.website || '',
          userLevel: data.data.userLevel,
          status: data.data.status,
          profileVisibility: data.data.profileVisibility,
          allowDirectMessages: data.data.allowDirectMessages,
          showOnlineStatus: data.data.showOnlineStatus,
          emailNotifications: data.data.emailNotifications,
          pushNotifications: data.data.pushNotifications,
          marketingEmails: data.data.marketingEmails,
          defaultImageQuality: data.data.defaultImageQuality,
          autoCompress: data.data.autoCompress,
          defaultImageFormat: data.data.defaultImageFormat,
          maxImageSize: data.data.maxImageSize,
          twoFactorEnabled: data.data.twoFactorEnabled,
          loginNotifications: data.data.loginNotifications,
        });
      } else {
        throw new Error(data.error?.message || '获取用户详情失败');
      }
    } catch (error) {
      console.error('加载用户详情失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  const loadActivityLogs = async () => {
    if (!userId) return;

    try {
      setActivityLoading(true);
      const token = localStorage.getItem('auth_token');
      if (!token) return;

      // 这里需要创建一个管理员专用的API来获取用户活动日志
      const response = await fetch(`/api/admin/users/${userId}/activity-logs?limit=50`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setActivityLogs(data.data.logs || []);
        }
      }
    } catch (error) {
      console.error('加载活动日志失败:', error);
    } finally {
      setActivityLoading(false);
    }
  };

  const loadLoginHistory = async () => {
    if (!userId) return;

    try {
      setLoginLoading(true);
      const token = localStorage.getItem('auth_token');
      if (!token) return;

      // 这里需要创建一个管理员专用的API来获取用户登录历史
      const response = await fetch(`/api/admin/users/${userId}/login-history?limit=50`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setLoginHistory(data.data.history || []);
        }
      }
    } catch (error) {
      console.error('加载登录历史失败:', error);
    } finally {
      setLoginLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
    setSuccessMessage(null);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      // 分别调用不同的API更新不同类型的信息

      // 1. 更新基本信息（如果是基本信息标签页）
      if (activeTab === 'profile') {
        const profileData = {
          displayName: formData.displayName,
          bio: formData.bio,
          location: formData.location,
          website: formData.website,
          profileVisibility: formData.profileVisibility,
          allowDirectMessages: formData.allowDirectMessages,
          showOnlineStatus: formData.showOnlineStatus,
        };

        const response = await fetch(`/api/admin/users/${userId}/profile`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(profileData),
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error?.message || '更新基本信息失败');
        }
      }

      // 2. 更新用户状态和等级
      const statusResponse = await fetch(`/api/admin/users/${userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          status: formData.status,
          userLevel: formData.userLevel,
        }),
      });

      if (!statusResponse.ok) {
        const data = await statusResponse.json();
        throw new Error(data.error?.message || '更新用户状态失败');
      }

      // 重新加载用户信息
      await loadUserDetails();
      setIsEditing(false);
      setSuccessMessage('用户信息更新成功！');

      if (onUserUpdated && user) {
        onUserUpdated({ ...user, ...formData });
      }

      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('保存失败:', error);
      setError(error instanceof Error ? error.message : '保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (user) {
      setFormData({
        displayName: user.displayName || '',
        bio: user.bio || '',
        location: user.location || '',
        website: user.website || '',
        userLevel: user.userLevel,
        status: user.status,
        profileVisibility: user.profileVisibility,
        allowDirectMessages: user.allowDirectMessages,
        showOnlineStatus: user.showOnlineStatus,
        emailNotifications: user.emailNotifications,
        pushNotifications: user.pushNotifications,
        marketingEmails: user.marketingEmails,
        defaultImageQuality: user.defaultImageQuality,
        autoCompress: user.autoCompress,
        defaultImageFormat: user.defaultImageFormat,
        maxImageSize: user.maxImageSize,
        twoFactorEnabled: user.twoFactorEnabled,
        loginNotifications: user.loginNotifications,
      });
    }
    setIsEditing(false);
    setError(null);
    setSuccessMessage(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getActivityIcon = (activityType: string) => {
    const iconMap: Record<string, string> = {
      login: '🔐',
      logout: '🚪',
      upload: '📤',
      delete: '🗑️',
      settings_change: '⚙️',
      password_change: '🔒',
      profile_update: '👤',
      default: '📝',
    };
    return iconMap[activityType] || iconMap.default;
  };

  const getActivityLabel = (activityType: string) => {
    const labelMap: Record<string, string> = {
      login: '登录',
      logout: '登出',
      upload: '上传文件',
      delete: '删除文件',
      settings_change: '设置更改',
      password_change: '密码修改',
      profile_update: '资料更新',
    };
    return labelMap[activityType] || activityType;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
              {user?.avatarUrl ? (
                <img
                  src={user.avatarUrl}
                  alt="头像"
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <span className="text-lg text-gray-600">
                  {user?.username?.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900">
                {user?.displayName || user?.username || '用户详情'}
              </h2>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary">{user?.userLevel}</Badge>
                <Badge variant={user?.status === 'active' ? 'success' : 'error'}>
                  {user?.status}
                </Badge>
                <Badge variant="outline">{user?.role}</Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {!isEditing ? (
              <Button size="sm" onClick={() => setIsEditing(true)}>
                编辑
              </Button>
            ) : (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  取消
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? '保存中...' : '保存'}
                </Button>
              </>
            )}
            <Button size="sm" variant="outline" onClick={onClose}>
              关闭
            </Button>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-xs ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="text-center py-8">
              <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
              <p className="text-gray-600">正在加载用户信息...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={loadUserDetails}>重试</Button>
            </div>
          ) : (
            <>
              {/* 成功消息 */}
              {successMessage && (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-xs text-green-600">{successMessage}</p>
                </div>
              )}

              {/* 错误消息 */}
              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-xs text-red-600">{error}</p>
                </div>
              )}

              {/* 标签页内容 */}
              {activeTab === 'profile' && user && (
                <div className="space-y-6">
                  {/* 基本信息 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>基本信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            用户名
                          </label>
                          <p className="text-xs text-gray-900">{user.username}</p>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            邮箱
                          </label>
                          <p className="text-xs text-gray-900">{user.email}</p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          显示名称
                        </label>
                        {isEditing ? (
                          <input
                            type="text"
                            value={formData.displayName}
                            onChange={(e) => handleInputChange('displayName', e.target.value)}
                            className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入显示名称"
                            maxLength={100}
                          />
                        ) : (
                          <p className="text-xs text-gray-900">
                            {user.displayName || '未设置'}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          个人简介
                        </label>
                        {isEditing ? (
                          <textarea
                            value={formData.bio}
                            onChange={(e) => handleInputChange('bio', e.target.value)}
                            className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="个人简介"
                            rows={3}
                            maxLength={500}
                          />
                        ) : (
                          <p className="text-xs text-gray-900">
                            {user.bio || '未设置'}
                          </p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            位置
                          </label>
                          {isEditing ? (
                            <input
                              type="text"
                              value={formData.location}
                              onChange={(e) => handleInputChange('location', e.target.value)}
                              className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="位置"
                              maxLength={100}
                            />
                          ) : (
                            <p className="text-xs text-gray-900">
                              {user.location || '未设置'}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            网站
                          </label>
                          {isEditing ? (
                            <input
                              type="url"
                              value={formData.website}
                              onChange={(e) => handleInputChange('website', e.target.value)}
                              className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="https://example.com"
                              maxLength={200}
                            />
                          ) : (
                            <p className="text-xs text-gray-900">
                              {user.website ? (
                                <a
                                  href={user.website}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  {user.website}
                                </a>
                              ) : (
                                '未设置'
                              )}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 账户状态 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>账户状态</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            用户等级
                          </label>
                          {isEditing ? (
                            <select
                              value={formData.userLevel}
                              onChange={(e) => handleInputChange('userLevel', e.target.value)}
                              className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              <option value="free">免费用户</option>
                              <option value="vip1">VIP 1</option>
                              <option value="vip2">VIP 2</option>
                              <option value="vip3">VIP 3</option>
                            </select>
                          ) : (
                            <Badge variant="secondary">{user.userLevel}</Badge>
                          )}
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            账户状态
                          </label>
                          {isEditing ? (
                            <select
                              value={formData.status}
                              onChange={(e) => handleInputChange('status', e.target.value)}
                              className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              <option value="active">正常</option>
                              <option value="suspended">暂停</option>
                              <option value="banned">封禁</option>
                            </select>
                          ) : (
                            <Badge variant={user.status === 'active' ? 'success' : 'error'}>
                              {user.status}
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="text-gray-500">注册时间</span>
                          <p className="text-gray-900 mt-1">
                            {formatDate(user.createdAt)}
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500">最后登录</span>
                          <p className="text-gray-900 mt-1">
                            {user.lastLoginAt
                              ? formatDate(user.lastLoginAt)
                              : '从未登录'
                            }
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500">最后活动</span>
                          <p className="text-gray-900 mt-1">
                            {user.lastActiveAt
                              ? formatDate(user.lastActiveAt)
                              : '无记录'
                            }
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500">等级过期</span>
                          <p className="text-gray-900 mt-1">
                            {user.levelExpiresAt
                              ? formatDate(user.levelExpiresAt)
                              : '永久'
                            }
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 统计信息 */}
                  {user._count && (
                    <Card>
                      <CardHeader>
                        <CardTitle>使用统计</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-4 gap-4">
                          <div className="text-center p-3 bg-blue-50 rounded-lg">
                            <div className="text-xl font-bold text-blue-600">
                              {user._count.userImages}
                            </div>
                            <div className="text-xs text-blue-600 mt-1">上传图片</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded-lg">
                            <div className="text-xl font-bold text-green-600">
                              {user._count.uploadLogs}
                            </div>
                            <div className="text-xs text-green-600 mt-1">上传记录</div>
                          </div>
                          <div className="text-center p-3 bg-yellow-50 rounded-lg">
                            <div className="text-xl font-bold text-yellow-600">
                              {user._count.activityLogs}
                            </div>
                            <div className="text-xs text-yellow-600 mt-1">活动记录</div>
                          </div>
                          <div className="text-center p-3 bg-purple-50 rounded-lg">
                            <div className="text-xl font-bold text-purple-600">
                              {user._count.loginHistory}
                            </div>
                            <div className="text-xs text-purple-600 mt-1">登录记录</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}

              {/* 活动记录标签页 */}
              {activeTab === 'activity' && (
                <Card>
                  <CardHeader>
                    <CardTitle>用户活动记录</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {activityLoading ? (
                      <div className="text-center py-8">
                        <div className="loading-spinner w-6 h-6 mx-auto mb-4" />
                        <p className="text-xs text-gray-600">正在加载活动记录...</p>
                      </div>
                    ) : activityLogs.length > 0 ? (
                      <div className="space-y-3">
                        {activityLogs.map((log) => (
                          <div key={log.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                            <span className="text-lg">{getActivityIcon(log.activityType)}</span>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="text-xs font-medium text-gray-900">
                                  {getActivityLabel(log.activityType)}
                                </h4>
                                <span className="text-xs text-gray-500">
                                  {formatDate(log.createdAt)}
                                </span>
                              </div>
                              <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                {log.ipAddress && (
                                  <span>IP: {log.ipAddress}</span>
                                )}
                                {log.location && (
                                  <span>位置: {log.location}</span>
                                )}
                              </div>
                              {log.activityData && (
                                <details className="mt-2">
                                  <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                                    查看详细信息
                                  </summary>
                                  <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                                    {JSON.stringify(log.activityData, null, 2)}
                                  </pre>
                                </details>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-xs text-gray-500">暂无活动记录</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* 登录历史标签页 */}
              {activeTab === 'login' && (
                <Card>
                  <CardHeader>
                    <CardTitle>登录历史</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loginLoading ? (
                      <div className="text-center py-8">
                        <div className="loading-spinner w-6 h-6 mx-auto mb-4" />
                        <p className="text-xs text-gray-600">正在加载登录历史...</p>
                      </div>
                    ) : loginHistory.length > 0 ? (
                      <div className="space-y-3">
                        {loginHistory.map((login) => (
                          <div key={login.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                            <span className="text-lg">
                              {login.isSuccess ? '✅' : '❌'}
                            </span>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <h4 className="text-xs font-medium text-gray-900">
                                    {login.isSuccess ? '登录成功' : '登录失败'}
                                  </h4>
                                  <Badge variant={login.isSuccess ? 'success' : 'error'}>
                                    {login.loginType}
                                  </Badge>
                                </div>
                                <span className="text-xs text-gray-500">
                                  {formatDate(login.createdAt)}
                                </span>
                              </div>
                              <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                <span>IP: {login.ipAddress}</span>
                                {login.location && (
                                  <span>位置: {login.location}</span>
                                )}
                              </div>
                              {!login.isSuccess && login.failureReason && (
                                <p className="mt-1 text-xs text-red-600">
                                  失败原因: {login.failureReason}
                                </p>
                              )}
                              {login.deviceInfo && (
                                <details className="mt-2">
                                  <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                                    查看设备信息
                                  </summary>
                                  <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                                    {JSON.stringify(login.deviceInfo, null, 2)}
                                  </pre>
                                </details>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-xs text-gray-500">暂无登录记录</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* 用户设置标签页 */}
              {activeTab === 'settings' && user && (
                <div className="space-y-6">
                  {/* 隐私设置 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>隐私设置</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          资料可见性
                        </label>
                        {isEditing ? (
                          <select
                            value={formData.profileVisibility}
                            onChange={(e) => handleInputChange('profileVisibility', e.target.value)}
                            className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="public">公开</option>
                            <option value="private">私密</option>
                            <option value="friends">仅好友</option>
                          </select>
                        ) : (
                          <p className="text-xs text-gray-900">
                            {formData.profileVisibility === 'public' && '公开'}
                            {formData.profileVisibility === 'private' && '私密'}
                            {formData.profileVisibility === 'friends' && '仅好友'}
                          </p>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-xs font-medium text-gray-700">允许私信</label>
                          <p className="text-xs text-gray-500">其他用户可以向该用户发送私信</p>
                        </div>
                        {isEditing ? (
                          <input
                            type="checkbox"
                            checked={formData.allowDirectMessages}
                            onChange={(e) => handleInputChange('allowDirectMessages', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        ) : (
                          <Badge variant={user.allowDirectMessages ? 'success' : 'secondary'}>
                            {user.allowDirectMessages ? '开启' : '关闭'}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-xs font-medium text-gray-700">显示在线状态</label>
                          <p className="text-xs text-gray-500">其他用户可以看到该用户的在线状态</p>
                        </div>
                        {isEditing ? (
                          <input
                            type="checkbox"
                            checked={formData.showOnlineStatus}
                            onChange={(e) => handleInputChange('showOnlineStatus', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        ) : (
                          <Badge variant={user.showOnlineStatus ? 'success' : 'secondary'}>
                            {user.showOnlineStatus ? '开启' : '关闭'}
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 通知设置 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>通知设置</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-xs font-medium text-gray-700">邮件通知</label>
                          <p className="text-xs text-gray-500">接收重要的系统通知和更新</p>
                        </div>
                        <Badge variant={user.emailNotifications ? 'success' : 'secondary'}>
                          {user.emailNotifications ? '开启' : '关闭'}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-xs font-medium text-gray-700">推送通知</label>
                          <p className="text-xs text-gray-500">在浏览器中接收实时通知</p>
                        </div>
                        <Badge variant={user.pushNotifications ? 'success' : 'secondary'}>
                          {user.pushNotifications ? '开启' : '关闭'}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-xs font-medium text-gray-700">营销邮件</label>
                          <p className="text-xs text-gray-500">接收产品更新和优惠信息</p>
                        </div>
                        <Badge variant={user.marketingEmails ? 'success' : 'secondary'}>
                          {user.marketingEmails ? '开启' : '关闭'}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-xs font-medium text-gray-700">登录通知</label>
                          <p className="text-xs text-gray-500">当有新设备登录时通知用户</p>
                        </div>
                        <Badge variant={user.loginNotifications ? 'success' : 'secondary'}>
                          {user.loginNotifications ? '开启' : '关闭'}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 上传设置 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>上传设置</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            默认图片质量
                          </label>
                          <p className="text-xs text-gray-900">{user.defaultImageQuality}%</p>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            默认图片格式
                          </label>
                          <p className="text-xs text-gray-900">{user.defaultImageFormat}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            自动压缩
                          </label>
                          <Badge variant={user.autoCompress ? 'success' : 'secondary'}>
                            {user.autoCompress ? '开启' : '关闭'}
                          </Badge>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            最大文件大小
                          </label>
                          <p className="text-xs text-gray-900">{formatFileSize(user.maxImageSize)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* 安全信息标签页 */}
              {activeTab === 'security' && user && (
                <Card>
                  <CardHeader>
                    <CardTitle>安全信息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-xs font-medium text-gray-700">两步验证</label>
                        <p className="text-xs text-gray-500">额外的账户安全保护</p>
                      </div>
                      <Badge variant={user.twoFactorEnabled ? 'success' : 'secondary'}>
                        {user.twoFactorEnabled ? '已启用' : '未启用'}
                      </Badge>
                    </div>

                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h4 className="text-xs font-medium text-yellow-800 mb-2">安全建议</h4>
                      <ul className="text-xs text-yellow-700 space-y-1">
                        <li>• 定期更换密码</li>
                        <li>• 启用两步验证</li>
                        <li>• 监控登录活动</li>
                        <li>• 避免在公共设备上登录</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}