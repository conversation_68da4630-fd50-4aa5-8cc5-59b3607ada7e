import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
export declare const validateJoiRequest: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateQuery: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateParams: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateFileUpload: (options: {
    maxSize?: number;
    allowedMimeTypes?: string[];
    required?: boolean;
}) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validatePagination: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateRequest: (req: Request, res: Response, next: NextFunction) => void;
declare global {
    namespace Express {
        interface Request {
            pagination?: {
                page: number;
                limit: number;
                offset: number;
                sortBy?: string;
                sortOrder: 'asc' | 'desc';
            };
        }
    }
}
//# sourceMappingURL=validation.middleware.d.ts.map