import { Router } from 'express';
import { LevelApplicationController } from '../controllers/levelApplication.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { body, query, param } from 'express-validator';

const router = Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 添加请求日志中间件
router.use((req, res, next) => {
  console.log(`[LevelApplication] ${req.method} ${req.path}`, {
    query: req.query,
    body: req.body,
    userId: (req as any).user?.id
  });
  next();
});

/**
 * @route   GET /api/level-application/configs
 * @desc    获取用户等级配置信息
 * @access  Private
 */
router.get('/configs', LevelApplicationController.getLevelConfigs);

/**
 * @route   POST /api/level-application/submit
 * @desc    提交等级申请
 * @access  Private
 */
router.post('/submit', [
  body('requestedLevel')
    .notEmpty()
    .withMessage('申请等级是必需的')
    .isIn(['free', 'vip1', 'vip2', 'vip3'])
    .withMessage('无效的等级申请'),
  body('reason')
    .notEmpty()
    .withMessage('申请理由是必需的')
    .isLength({ min: 10, max: 1000 })
    .withMessage('申请理由长度应在10-1000字符之间'),
  body('contactInfo')
    .optional()
    .isLength({ max: 200 })
    .withMessage('联系方式不能超过200个字符'),
  body('businessInfo')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('业务信息不能超过1000个字符'),
  body('expectedUsage')
    .optional()
    .isLength({ max: 500 })
    .withMessage('预期使用量不能超过500个字符'),
  validateRequest
], LevelApplicationController.submitApplication);

/**
 * @route   GET /api/level-application/my-applications
 * @desc    获取用户的申请历史
 * @access  Private
 */
router.get('/my-applications', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('每页数量必须在1-50之间'),
  validateRequest
], LevelApplicationController.getUserApplications);

/**
 * @route   PUT /api/level-application/:applicationId/cancel
 * @desc    取消申请
 * @access  Private
 */
router.put('/:applicationId/cancel', [
  param('applicationId')
    .isInt({ min: 1 })
    .withMessage('申请ID必须是正整数'),
  validateRequest
], LevelApplicationController.cancelApplication);

export default router;
