import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface LevelApplication {
  id: number;
  currentLevel: string;
  requestedLevel: string;
  reason: string;
  contactInfo?: string;
  businessInfo?: string;
  expectedUsage?: string;
  status: string;
  adminComment?: string;
  processedAt?: string;
  createdAt: string;
  user: {
    id: number;
    username: string;
    email: string;
    displayName?: string;
    userLevel: string;
    createdAt: string;
  };
  admin?: {
    username: string;
    displayName?: string;
  };
}

interface ApplicationsResponse {
  applications: LevelApplication[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export function LevelApplicationManagement() {
  const [applications, setApplications] = useState<LevelApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState('all');
  const [levelFilter, setLevelFilter] = useState('all');
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<LevelApplication | null>(null);
  const [showProcessModal, setShowProcessModal] = useState(false);
  const [processAction, setProcessAction] = useState<'approve' | 'reject'>('approve');
  const [adminComment, setAdminComment] = useState('');

  useEffect(() => {
    loadApplications();
  }, [currentPage, statusFilter, levelFilter]);

  const loadApplications = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        status: statusFilter,
        requestedLevel: levelFilter,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      const response = await fetch(`/api/admin/level-applications?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取申请列表失败');
      }

      const data = await response.json();
      if (data.success) {
        setApplications(data.data.applications);
        setTotalPages(data.data.pagination.totalPages);
      } else {
        throw new Error(data.error?.message || '获取申请列表失败');
      }

    } catch (error) {
      console.error('加载申请列表失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleProcessApplication = async () => {
    if (!selectedApplication) return;

    try {
      setProcessing(selectedApplication.id);
      setError(null);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch(`/api/admin/level-applications/${selectedApplication.id}/process`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          action: processAction,
          comment: adminComment.trim() || null,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '处理申请失败');
      }

      if (data.success) {
        setSuccessMessage(`申请已${processAction === 'approve' ? '批准' : '拒绝'}`);
        setShowProcessModal(false);
        setSelectedApplication(null);
        setAdminComment('');
        loadApplications();
        setTimeout(() => setSuccessMessage(null), 3000);
      } else {
        throw new Error(data.error?.message || '处理申请失败');
      }

    } catch (error) {
      console.error('处理申请失败:', error);
      setError(error instanceof Error ? error.message : '处理申请失败');
    } finally {
      setProcessing(null);
    }
  };

  const openProcessModal = (application: LevelApplication, action: 'approve' | 'reject') => {
    setSelectedApplication(application);
    setProcessAction(action);
    setAdminComment('');
    setShowProcessModal(true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      pending: { variant: 'secondary' as const, label: '待审核' },
      approved: { variant: 'success' as const, label: '已批准' },
      rejected: { variant: 'error' as const, label: '已拒绝' },
      cancelled: { variant: 'outline' as const, label: '已取消' },
    };
    
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getLevelDisplayName = (level: string) => {
    const levelMap: Record<string, string> = {
      free: '免费用户',
      vip1: 'VIP 1',
      vip2: 'VIP 2',
      vip3: 'VIP 3',
    };
    return levelMap[level] || level;
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
        <p className="text-gray-600">正在加载申请列表...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 成功消息 */}
      {successMessage && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-600">{successMessage}</p>
        </div>
      )}

      {/* 错误消息 */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle>等级申请管理</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                申请状态
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">全部状态</option>
                <option value="pending">待审核</option>
                <option value="approved">已批准</option>
                <option value="rejected">已拒绝</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                申请等级
              </label>
              <select
                value={levelFilter}
                onChange={(e) => {
                  setLevelFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">全部等级</option>
                <option value="vip1">VIP 1</option>
                <option value="vip2">VIP 2</option>
                <option value="vip3">VIP 3</option>
              </select>
            </div>

            <div className="flex items-end">
              <Button size="sm" onClick={loadApplications}>
                刷新
              </Button>
            </div>
          </div>

          {/* 申请列表 */}
          {applications.length > 0 ? (
            <div className="space-y-4">
              {applications.map((application) => (
                <div key={application.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-medium text-sm">
                          {application.user.displayName || application.user.username}
                        </span>
                        <Badge variant="outline">{application.user.email}</Badge>
                        <span className="text-xs text-gray-500">
                          {getLevelDisplayName(application.currentLevel)} → {getLevelDisplayName(application.requestedLevel)}
                        </span>
                        {getStatusBadge(application.status)}
                      </div>
                      
                      <div className="text-xs text-gray-600 space-y-1">
                        <p>申请时间: {formatDate(application.createdAt)}</p>
                        <p>用户注册: {formatDate(application.user.createdAt)}</p>
                        {application.processedAt && (
                          <p>
                            处理时间: {formatDate(application.processedAt)}
                            {application.admin && (
                              <span className="ml-2">
                                处理人: {application.admin.displayName || application.admin.username}
                              </span>
                            )}
                          </p>
                        )}
                      </div>
                    </div>

                    {application.status === 'pending' && (
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          onClick={() => openProcessModal(application, 'approve')}
                          disabled={processing === application.id}
                        >
                          批准
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openProcessModal(application, 'reject')}
                          disabled={processing === application.id}
                        >
                          拒绝
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="space-y-3 text-xs">
                    <div>
                      <span className="font-medium text-gray-700">申请理由:</span>
                      <p className="text-gray-600 mt-1">{application.reason}</p>
                    </div>

                    {application.contactInfo && (
                      <div>
                        <span className="font-medium text-gray-700">联系方式:</span>
                        <p className="text-gray-600 mt-1">{application.contactInfo}</p>
                      </div>
                    )}

                    {application.businessInfo && (
                      <div>
                        <span className="font-medium text-gray-700">业务信息:</span>
                        <p className="text-gray-600 mt-1">{application.businessInfo}</p>
                      </div>
                    )}

                    {application.expectedUsage && (
                      <div>
                        <span className="font-medium text-gray-700">预期使用量:</span>
                        <p className="text-gray-600 mt-1">{application.expectedUsage}</p>
                      </div>
                    )}

                    {application.adminComment && (
                      <div>
                        <span className="font-medium text-gray-700">管理员备注:</span>
                        <p className="text-gray-600 mt-1">{application.adminComment}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-sm text-gray-500">暂无申请记录</p>
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-xs text-gray-500">
                第 {currentPage} 页，共 {totalPages} 页
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 处理申请模态框 */}
      {showProcessModal && selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                {processAction === 'approve' ? '批准申请' : '拒绝申请'}
              </h3>

              <div className="space-y-4">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm">
                    <p><span className="font-medium">用户:</span> {selectedApplication.user.displayName || selectedApplication.user.username}</p>
                    <p><span className="font-medium">申请:</span> {getLevelDisplayName(selectedApplication.currentLevel)} → {getLevelDisplayName(selectedApplication.requestedLevel)}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {processAction === 'approve' ? '批准' : '拒绝'}理由 (可选)
                  </label>
                  <textarea
                    value={adminComment}
                    onChange={(e) => setAdminComment(e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder={processAction === 'approve'
                      ? '请输入批准理由或备注...'
                      : '请输入拒绝理由...'
                    }
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {adminComment.length}/500 字符
                  </p>
                </div>

                {processAction === 'approve' && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-xs text-green-700">
                      ⚠️ 批准后将立即更新用户等级，并设置1年有效期
                    </p>
                  </div>
                )}
              </div>

              <div className="flex space-x-3 mt-6">
                <Button
                  onClick={handleProcessApplication}
                  disabled={processing === selectedApplication.id}
                  variant={processAction === 'approve' ? 'primary' : 'error'}
                >
                  {processing === selectedApplication.id
                    ? '处理中...'
                    : (processAction === 'approve' ? '确认批准' : '确认拒绝')
                  }
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowProcessModal(false);
                    setSelectedApplication(null);
                    setAdminComment('');
                  }}
                  disabled={processing === selectedApplication.id}
                >
                  取消
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
