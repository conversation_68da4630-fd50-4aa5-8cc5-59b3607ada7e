import { EventEmitter } from 'events';
import { SocketService } from '../config/socket';
import type { SystemAlert } from '../config/socket';
export interface SystemMetrics {
    cpu: {
        usage: number;
        cores: number;
        loadAverage: number[];
    };
    memory: {
        total: number;
        used: number;
        free: number;
        percentage: number;
    };
    disk: {
        total: number;
        used: number;
        free: number;
        percentage: number;
    };
    network: {
        bytesIn: number;
        bytesOut: number;
    };
    business: {
        totalUsers: number;
        activeUsers: number;
        onlineUsers: number;
        todayUploads: number;
        totalUploads: number;
        totalStorage: number;
    };
    system: {
        uptime: number;
        timestamp: string;
        serverInstance: string;
    };
}
export interface AlertThreshold {
    cpu: number;
    memory: number;
    disk: number;
    errorRate: number;
}
export declare class SystemMonitorService extends EventEmitter {
    private metricsInterval;
    private socketService;
    private lastNetworkStats;
    private alertThresholds;
    constructor(socketService?: SocketService);
    startMonitoring(intervalMs?: number): void;
    stopMonitoring(): void;
    private collectAndBroadcastMetrics;
    collectSystemMetrics(): Promise<SystemMetrics>;
    private getCPUUsage;
    private cpuAverage;
    private getMemoryUsage;
    private getDiskUsage;
    private getNetworkStats;
    private getBusinessMetrics;
    private saveMetricsToDatabase;
    private saveMetric;
    private checkAlerts;
    private emitAlert;
    setAlertThresholds(thresholds: Partial<AlertThreshold>): void;
    getAlertThresholds(): AlertThreshold;
    logSystemEvent(eventType: string, level: 'debug' | 'info' | 'warning' | 'error' | 'critical', message: string, data?: any): Promise<void>;
    getHistoricalMetrics(metricType: string, startTime: Date, endTime: Date, limit?: number): Promise<any[]>;
    getSystemEvents(eventLevel?: string, startTime?: Date, endTime?: Date, limit?: number): Promise<any[]>;
    cleanupOldData(daysToKeep?: number): Promise<void>;
    private setupEventListeners;
    getSystemSummary(): Promise<{
        status: 'healthy' | 'warning' | 'critical';
        metrics: SystemMetrics;
        alerts: SystemAlert[];
    }>;
}
//# sourceMappingURL=system-monitor.service.d.ts.map