const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createLevelConfigs() {
  try {
    console.log('🔄 创建等级配置...');

    // 创建用户等级配置
    const levelConfigs = [
      {
        level: 'free',
        displayName: '免费用户',
        maxDailyUploads: 10,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        maxStorageSpace: 1024 * 1024 * 1024, // 1GB
        canChooseProvider: false,
        prioritySupport: false,
        customDomain: false,
        visibleProviderCount: 1
      },
      {
        level: 'vip1',
        displayName: 'VIP 1',
        maxDailyUploads: 50,
        maxFileSize: 20 * 1024 * 1024, // 20MB
        maxStorageSpace: 10 * 1024 * 1024 * 1024, // 10GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: false,
        visibleProviderCount: 2
      },
      {
        level: 'vip2',
        displayName: 'VIP 2',
        maxDailyUploads: 200,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        maxStorageSpace: 50 * 1024 * 1024 * 1024, // 50GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 3
      },
      {
        level: 'vip3',
        displayName: 'VIP 3',
        maxDailyUploads: 1000,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        maxStorageSpace: 200 * 1024 * 1024 * 1024, // 200GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 4
      }
    ];

    for (const config of levelConfigs) {
      await prisma.userLevelConfig.upsert({
        where: { level: config.level },
        update: config,
        create: config
      });
      console.log(`✅ 已创建等级配置: ${config.displayName}`);
    }

    console.log('\n🎉 等级配置创建完成！');

  } catch (error) {
    console.error('❌ 创建等级配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行创建脚本
createLevelConfigs();
