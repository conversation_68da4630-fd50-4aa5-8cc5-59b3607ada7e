import { Request, Response } from 'express';
export declare class LevelApplicationController {
    static getLevelConfigs(req: Request, res: Response): Promise<void>;
    static submitApplication(req: Request, res: Response): Promise<void>;
    static getUserApplications(req: Request, res: Response): Promise<void>;
    static cancelApplication(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=levelApplication.controller.d.ts.map