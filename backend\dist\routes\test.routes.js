"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const log_service_1 = require("../services/log.service");
const router = (0, express_1.Router)();
router.post('/logs/generate', async (req, res) => {
    try {
        await log_service_1.LogService.logSystemEvent('test_event', 'info', '这是一个测试系统日志', { testData: 'Hello World', timestamp: new Date().toISOString() });
        await log_service_1.LogService.logUploadAction(1, 123, 'upload', 'test-image.jpg', 1024 * 1024, req.ip, req.get('User-Agent'), true);
        await log_service_1.LogService.logSystemEvent('server_status', 'warning', '服务器负载较高', { cpu: 85, memory: 78, disk: 45 });
        await log_service_1.LogService.logAdminOperation(1, 'test_operation', 'user', 123, { action: 'test', details: 'This is a test admin operation' }, req.ip, req.get('User-Agent'));
        res.json({
            success: true,
            message: '测试日志已生成',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('生成测试日志失败:', error);
        res.status(500).json({
            success: false,
            error: '生成测试日志失败'
        });
    }
});
exports.default = router;
//# sourceMappingURL=test.routes.js.map