import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface UserProfile {
  defaultImageQuality: number;
  autoCompress: boolean;
  defaultImageFormat: string;
  maxImageSize: number;
}

interface UploadSettingsProps {
  userProfile: UserProfile;
  onUpdate: (updatedData: Partial<UserProfile>) => void;
}

export function UploadSettings({ userProfile, onUpdate }: UploadSettingsProps) {
  const [formData, setFormData] = useState({
    defaultImageQuality: userProfile.defaultImageQuality,
    autoCompress: userProfile.autoCompress,
    defaultImageFormat: userProfile.defaultImageFormat,
    maxImageSize: userProfile.maxImageSize,
  });
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch('/api/user/upload-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('更新失败');
      }

      const data = await response.json();
      if (data.success) {
        onUpdate(data.data);
        setHasChanges(false);
      } else {
        throw new Error(data.error?.message || '更新失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      alert(error instanceof Error ? error.message : '保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setFormData({
      defaultImageQuality: userProfile.defaultImageQuality,
      autoCompress: userProfile.autoCompress,
      defaultImageFormat: userProfile.defaultImageFormat,
      maxImageSize: userProfile.maxImageSize,
    });
    setHasChanges(false);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getQualityLabel = (quality: number) => {
    if (quality >= 90) return '极高';
    if (quality >= 80) return '高';
    if (quality >= 60) return '中等';
    if (quality >= 40) return '低';
    return '极低';
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 80) return 'text-green-600';
    if (quality >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>上传设置</CardTitle>
            {hasChanges && (
              <div className="flex space-x-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleReset}
                  disabled={isSaving}
                >
                  重置
                </Button>
                <Button 
                  size="sm" 
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? '保存中...' : '保存更改'}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 图片质量设置 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-3">
              默认图片质量
            </label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">质量等级</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">{formData.defaultImageQuality}%</Badge>
                  <span className={`text-xs font-medium ${getQualityColor(formData.defaultImageQuality)}`}>
                    {getQualityLabel(formData.defaultImageQuality)}
                  </span>
                </div>
              </div>
              <input
                type="range"
                min="10"
                max="100"
                step="5"
                value={formData.defaultImageQuality}
                onChange={(e) => handleInputChange('defaultImageQuality', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>低质量 (10%)</span>
                <span>高质量 (100%)</span>
              </div>
              <p className="text-xs text-gray-500">
                较高的质量会产生更大的文件，但图片更清晰。建议设置为 80-90%。
              </p>
            </div>
          </div>

          {/* 自动压缩 */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <h4 className="text-xs font-medium text-gray-900">自动压缩</h4>
              <p className="text-xs text-gray-500 mt-1">
                自动压缩大图片以节省存储空间和加快上传速度
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant={formData.autoCompress ? 'success' : 'secondary'}>
                {formData.autoCompress ? '开启' : '关闭'}
              </Badge>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.autoCompress}
                  onChange={(e) => handleInputChange('autoCompress', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          {/* 默认图片格式 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              默认图片格式
            </label>
            <select
              value={formData.defaultImageFormat}
              onChange={(e) => handleInputChange('defaultImageFormat', e.target.value)}
              className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="original">保持原格式</option>
              <option value="webp">WebP (推荐，体积小)</option>
              <option value="jpeg">JPEG (兼容性好)</option>
              <option value="png">PNG (支持透明)</option>
            </select>
            <p className="mt-1 text-xs text-gray-500">
              WebP 格式可以显著减小文件大小，但某些旧设备可能不支持。
            </p>
          </div>

          {/* 最大文件大小 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-3">
              单个文件最大大小
            </label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">当前限制</span>
                <Badge variant="secondary">{formatFileSize(formData.maxImageSize)}</Badge>
              </div>
              <input
                type="range"
                min="1048576"
                max="52428800"
                step="1048576"
                value={formData.maxImageSize}
                onChange={(e) => handleInputChange('maxImageSize', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1 MB</span>
                <span>50 MB</span>
              </div>
              <p className="text-xs text-gray-500">
                较大的文件限制允许上传高质量图片，但会消耗更多存储空间。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 支持的格式 */}
      <Card>
        <CardHeader>
          <CardTitle>支持的文件格式</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { format: 'JPEG', description: '最常用的图片格式', supported: true },
              { format: 'PNG', description: '支持透明背景', supported: true },
              { format: 'WebP', description: '现代高效格式', supported: true },
              { format: 'GIF', description: '支持动画', supported: true },
              { format: 'BMP', description: '位图格式', supported: false },
              { format: 'TIFF', description: '高质量格式', supported: false },
              { format: 'SVG', description: '矢量图形', supported: false },
              { format: 'HEIC', description: 'iOS 格式', supported: false },
            ].map((item) => (
              <div key={item.format} className={`p-3 rounded-lg border ${
                item.supported ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
              }`}>
                <div className="flex items-center space-x-2 mb-1">
                  <span className={`text-xs font-medium ${
                    item.supported ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    {item.format}
                  </span>
                  <span className={`text-xs ${
                    item.supported ? 'text-green-500' : 'text-gray-400'
                  }`}>
                    {item.supported ? '✓' : '✗'}
                  </span>
                </div>
                <p className={`text-xs ${
                  item.supported ? 'text-green-600' : 'text-gray-500'
                }`}>
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 上传统计 */}
      <Card>
        <CardHeader>
          <CardTitle>上传统计</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-xl font-bold text-blue-600">0</div>
              <div className="text-xs text-blue-600 mt-1">今日上传</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-xl font-bold text-green-600">0</div>
              <div className="text-xs text-green-600 mt-1">总上传数</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-xl font-bold text-yellow-600">0 MB</div>
              <div className="text-xs text-yellow-600 mt-1">已用空间</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-xl font-bold text-purple-600">∞</div>
              <div className="text-xs text-purple-600 mt-1">总空间</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
