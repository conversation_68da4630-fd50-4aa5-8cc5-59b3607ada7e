"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMonitorService = void 0;
const events_1 = require("events");
const os = __importStar(require("os"));
const fs = __importStar(require("fs"));
const util_1 = require("util");
const database_1 = require("../config/database");
const stat = (0, util_1.promisify)(fs.stat);
class SystemMonitorService extends events_1.EventEmitter {
    constructor(socketService) {
        super();
        this.metricsInterval = null;
        this.socketService = null;
        this.lastNetworkStats = null;
        this.alertThresholds = {
            cpu: 80,
            memory: 85,
            disk: 90,
            errorRate: 5
        };
        this.socketService = socketService || null;
        this.setupEventListeners();
    }
    startMonitoring(intervalMs = 30000) {
        if (this.metricsInterval) {
            console.log('系统监控已在运行中');
            return;
        }
        this.metricsInterval = setInterval(async () => {
            try {
                await this.collectAndBroadcastMetrics();
            }
            catch (error) {
                console.error('收集系统指标失败:', error);
                await this.logSystemEvent('metric_collection_failed', 'error', error.message);
            }
        }, intervalMs);
        console.log(`系统监控已启动，采集间隔: ${intervalMs}ms`);
        this.logSystemEvent('monitoring_started', 'info', '系统监控服务已启动');
    }
    stopMonitoring() {
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            this.metricsInterval = null;
            console.log('系统监控已停止');
            this.logSystemEvent('monitoring_stopped', 'info', '系统监控服务已停止');
        }
    }
    async collectAndBroadcastMetrics() {
        const metrics = await this.collectSystemMetrics();
        await this.saveMetricsToDatabase(metrics);
        this.checkAlerts(metrics);
        if (this.socketService) {
            const monitoringData = {
                cpu: metrics.cpu.usage,
                memory: metrics.memory.percentage,
                disk: metrics.disk.percentage,
                network: metrics.network,
                activeConnections: metrics.business.onlineUsers,
                timestamp: metrics.system.timestamp
            };
            this.socketService.broadcastSystemMonitoring(monitoringData);
        }
    }
    async collectSystemMetrics() {
        const [cpuUsage, memoryUsage, diskUsage, networkStats, businessMetrics] = await Promise.all([
            this.getCPUUsage(),
            this.getMemoryUsage(),
            this.getDiskUsage(),
            this.getNetworkStats(),
            this.getBusinessMetrics()
        ]);
        return {
            cpu: cpuUsage,
            memory: memoryUsage,
            disk: diskUsage,
            network: networkStats,
            business: businessMetrics,
            system: {
                uptime: os.uptime(),
                timestamp: new Date().toISOString(),
                serverInstance: os.hostname()
            }
        };
    }
    async getCPUUsage() {
        return new Promise((resolve) => {
            const startMeasure = this.cpuAverage();
            setTimeout(() => {
                const endMeasure = this.cpuAverage();
                const idleDifference = endMeasure.idle - startMeasure.idle;
                const totalDifference = endMeasure.total - startMeasure.total;
                const usage = 100 - Math.floor(100 * idleDifference / totalDifference);
                resolve({
                    usage: Math.max(0, Math.min(100, usage)),
                    cores: os.cpus().length,
                    loadAverage: os.loadavg()
                });
            }, 1000);
        });
    }
    cpuAverage() {
        const cpus = os.cpus();
        let idle = 0;
        let total = 0;
        cpus.forEach(cpu => {
            Object.keys(cpu.times).forEach(type => {
                total += cpu.times[type];
            });
            idle += cpu.times.idle;
        });
        return { idle: idle / cpus.length, total: total / cpus.length };
    }
    getMemoryUsage() {
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        return {
            total: Math.round(totalMem / 1024 / 1024),
            used: Math.round(usedMem / 1024 / 1024),
            free: Math.round(freeMem / 1024 / 1024),
            percentage: Math.round((usedMem / totalMem) * 100)
        };
    }
    async getDiskUsage() {
        try {
            const stats = await stat(process.cwd());
            const total = 100 * 1024 * 1024 * 1024;
            const used = Math.floor(Math.random() * total * 0.8);
            const free = total - used;
            return {
                total: Math.round(total / 1024 / 1024),
                used: Math.round(used / 1024 / 1024),
                free: Math.round(free / 1024 / 1024),
                percentage: Math.round((used / total) * 100)
            };
        }
        catch (error) {
            console.error('获取磁盘使用情况失败:', error);
            return {
                total: 0,
                used: 0,
                free: 0,
                percentage: 0
            };
        }
    }
    getNetworkStats() {
        const currentStats = {
            bytesIn: Math.floor(Math.random() * 1000000),
            bytesOut: Math.floor(Math.random() * 1000000)
        };
        if (this.lastNetworkStats) {
            return {
                bytesIn: Math.max(0, currentStats.bytesIn - this.lastNetworkStats.bytesIn),
                bytesOut: Math.max(0, currentStats.bytesOut - this.lastNetworkStats.bytesOut)
            };
        }
        this.lastNetworkStats = currentStats;
        return { bytesIn: 0, bytesOut: 0 };
    }
    async getBusinessMetrics() {
        try {
            const [totalUsers, activeUsers, todayUploads, totalUploads, storageStats] = await Promise.all([
                database_1.prisma.user.count(),
                database_1.prisma.user.count({ where: { status: 'active' } }),
                database_1.prisma.userImage.count({
                    where: {
                        createdAt: {
                            gte: new Date(new Date().setHours(0, 0, 0, 0))
                        }
                    }
                }),
                database_1.prisma.userImage.count(),
                database_1.prisma.image.aggregate({
                    _sum: { fileSize: true },
                    where: { isDeleted: false }
                })
            ]);
            const onlineUsers = Math.floor(Math.random() * activeUsers * 0.3);
            return {
                totalUsers,
                activeUsers,
                onlineUsers,
                todayUploads,
                totalUploads,
                totalStorage: Number(storageStats._sum.fileSize || 0)
            };
        }
        catch (error) {
            console.error('获取业务指标失败:', error);
            return {
                totalUsers: 0,
                activeUsers: 0,
                onlineUsers: 0,
                todayUploads: 0,
                totalUploads: 0,
                totalStorage: 0
            };
        }
    }
    async saveMetricsToDatabase(metrics) {
        const serverInstance = metrics.system.serverInstance;
        try {
            await Promise.all([
                this.saveMetric('cpu_usage', metrics.cpu.usage, '%', serverInstance),
                this.saveMetric('memory_usage', metrics.memory.percentage, '%', serverInstance),
                this.saveMetric('disk_usage', metrics.disk.percentage, '%', serverInstance),
                this.saveMetric('network_in', metrics.network.bytesIn, 'bytes', serverInstance),
                this.saveMetric('network_out', metrics.network.bytesOut, 'bytes', serverInstance),
                this.saveMetric('total_users', metrics.business.totalUsers, 'count', serverInstance),
                this.saveMetric('active_users', metrics.business.activeUsers, 'count', serverInstance),
                this.saveMetric('online_users', metrics.business.onlineUsers, 'count', serverInstance),
                this.saveMetric('today_uploads', metrics.business.todayUploads, 'count', serverInstance),
                this.saveMetric('total_storage', metrics.business.totalStorage, 'bytes', serverInstance)
            ]);
        }
        catch (error) {
            console.error('保存指标到数据库失败:', error);
        }
    }
    async saveMetric(type, value, unit, serverInstance) {
        await database_1.prisma.systemMetric.create({
            data: {
                metricType: type,
                metricValue: value,
                unit,
                serverInstance
            }
        });
    }
    checkAlerts(metrics) {
        const alerts = [];
        if (metrics.cpu.usage > this.alertThresholds.cpu) {
            alerts.push({
                type: 'high_cpu',
                level: metrics.cpu.usage > 90 ? 'critical' : 'warning',
                message: `CPU使用率过高: ${metrics.cpu.usage}%`,
                value: metrics.cpu.usage,
                threshold: this.alertThresholds.cpu,
                timestamp: metrics.system.timestamp
            });
        }
        if (metrics.memory.percentage > this.alertThresholds.memory) {
            alerts.push({
                type: 'high_memory',
                level: metrics.memory.percentage > 95 ? 'critical' : 'warning',
                message: `内存使用率过高: ${metrics.memory.percentage}%`,
                value: metrics.memory.percentage,
                threshold: this.alertThresholds.memory,
                timestamp: metrics.system.timestamp
            });
        }
        if (metrics.disk.percentage > this.alertThresholds.disk) {
            alerts.push({
                type: 'high_disk',
                level: metrics.disk.percentage > 95 ? 'critical' : 'warning',
                message: `磁盘使用率过高: ${metrics.disk.percentage}%`,
                value: metrics.disk.percentage,
                threshold: this.alertThresholds.disk,
                timestamp: metrics.system.timestamp
            });
        }
        alerts.forEach(alert => {
            this.emitAlert(alert);
        });
    }
    emitAlert(alert) {
        this.emit('alert', alert);
        if (this.socketService) {
            this.socketService.broadcastSystemAlert(alert);
        }
        this.logSystemEvent(`alert_${alert.type}`, alert.level === 'critical' ? 'critical' : 'warning', alert.message, { threshold: alert.threshold, value: alert.value });
        console.warn(`[系统告警] ${alert.message}`);
    }
    setAlertThresholds(thresholds) {
        this.alertThresholds = { ...this.alertThresholds, ...thresholds };
        console.log('告警阈值已更新:', this.alertThresholds);
    }
    getAlertThresholds() {
        return { ...this.alertThresholds };
    }
    async logSystemEvent(eventType, level, message, data) {
        try {
            await database_1.prisma.systemEventLog.create({
                data: {
                    eventType,
                    eventLevel: level,
                    eventMessage: message,
                    eventData: data ? JSON.stringify(data) : null,
                    serverInstance: os.hostname()
                }
            });
        }
        catch (error) {
            console.error('记录系统事件失败:', error);
        }
    }
    async getHistoricalMetrics(metricType, startTime, endTime, limit = 100) {
        try {
            return await database_1.prisma.systemMetric.findMany({
                where: {
                    metricType,
                    createdAt: {
                        gte: startTime,
                        lte: endTime
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                },
                take: limit,
                select: {
                    metricValue: true,
                    unit: true,
                    createdAt: true,
                    serverInstance: true
                }
            });
        }
        catch (error) {
            console.error('获取历史指标数据失败:', error);
            return [];
        }
    }
    async getSystemEvents(eventLevel, startTime, endTime, limit = 50) {
        try {
            const where = {};
            if (eventLevel) {
                where.eventLevel = eventLevel;
            }
            if (startTime && endTime) {
                where.createdAt = {
                    gte: startTime,
                    lte: endTime
                };
            }
            return await database_1.prisma.systemEventLog.findMany({
                where,
                orderBy: {
                    createdAt: 'desc'
                },
                take: limit
            });
        }
        catch (error) {
            console.error('获取系统事件日志失败:', error);
            return [];
        }
    }
    async cleanupOldData(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        try {
            const [metricsDeleted, eventsDeleted] = await Promise.all([
                database_1.prisma.systemMetric.deleteMany({
                    where: {
                        createdAt: {
                            lt: cutoffDate
                        }
                    }
                }),
                database_1.prisma.systemEventLog.deleteMany({
                    where: {
                        createdAt: {
                            lt: cutoffDate
                        }
                    }
                })
            ]);
            console.log(`数据清理完成: 删除了 ${metricsDeleted.count} 条指标记录和 ${eventsDeleted.count} 条事件日志`);
            await this.logSystemEvent('data_cleanup', 'info', `清理了 ${daysToKeep} 天前的数据`, { metricsDeleted: metricsDeleted.count, eventsDeleted: eventsDeleted.count });
        }
        catch (error) {
            console.error('清理过期数据失败:', error);
            await this.logSystemEvent('data_cleanup_failed', 'error', error.message);
        }
    }
    setupEventListeners() {
        this.on('alert', (alert) => {
            console.log(`收到系统告警: ${alert.message}`);
        });
        process.on('SIGINT', () => {
            this.stopMonitoring();
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            this.stopMonitoring();
            process.exit(0);
        });
    }
    async getSystemSummary() {
        const metrics = await this.collectSystemMetrics();
        const alerts = [];
        let status = 'healthy';
        if (metrics.cpu.usage > this.alertThresholds.cpu ||
            metrics.memory.percentage > this.alertThresholds.memory ||
            metrics.disk.percentage > this.alertThresholds.disk) {
            status = 'warning';
        }
        if (metrics.cpu.usage > 90 ||
            metrics.memory.percentage > 95 ||
            metrics.disk.percentage > 95) {
            status = 'critical';
        }
        return {
            status,
            metrics,
            alerts
        };
    }
}
exports.SystemMonitorService = SystemMonitorService;
//# sourceMappingURL=system-monitor.service.js.map