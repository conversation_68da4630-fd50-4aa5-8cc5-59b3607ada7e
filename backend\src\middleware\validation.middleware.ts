import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import <PERSON><PERSON> from 'joi';
import { ApiResponse, ErrorCodes } from '../types';

// Joi 验证请求中间件
export const validateJoiRequest = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // 返回所有验证错误
      stripUnknown: true, // 移除未知字段
      convert: true, // 自动类型转换
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_INPUT,
          message: '请求数据验证失败',
          details: errorDetails,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    // 将验证后的数据替换原始请求体
    req.body = value;
    next();
  };
};

// 验证查询参数中间件
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_INPUT,
          message: '查询参数验证失败',
          details: errorDetails,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    req.query = value;
    next();
  };
};

// 验证路径参数中间件
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_INPUT,
          message: '路径参数验证失败',
          details: errorDetails,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    req.params = value;
    next();
  };
};

// 文件上传验证中间件
export const validateFileUpload = (options: {
  maxSize?: number;
  allowedMimeTypes?: string[];
  required?: boolean;
}) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const file = req.file;
    const { maxSize, allowedMimeTypes, required = true } = options;

    // 检查文件是否存在
    if (required && !file) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_INPUT,
          message: '文件不能为空',
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    if (!file) {
      next();
      return;
    }

    // 检查文件大小
    if (maxSize && file.size > maxSize) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.FILE_TOO_LARGE,
          message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    // 检查文件类型
    if (allowedMimeTypes && !allowedMimeTypes.includes(file.mimetype)) {
      res.status(400).json({
        success: false,
        error: {
          code: ErrorCodes.INVALID_FILE_TYPE,
          message: `不支持的文件类型，仅支持: ${allowedMimeTypes.join(', ')}`,
          timestamp: new Date().toISOString(),
          requestId: req.requestId,
        }
      } as ApiResponse);
      return;
    }

    next();
  };
};

// 分页参数验证中间件
export const validatePagination = (req: Request, res: Response, next: NextFunction): void => {
  const paginationSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  });

  const { error, value } = paginationSchema.validate(req.query, {
    stripUnknown: false,
    convert: true,
  });

  if (error) {
    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.INVALID_INPUT,
        message: '分页参数验证失败',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
        })),
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      }
    } as ApiResponse);
    return;
  }

  // 将验证后的分页参数添加到请求对象
  req.pagination = {
    page: value.page,
    limit: value.limit,
    offset: (value.page - 1) * value.limit,
    sortBy: value.sortBy,
    sortOrder: value.sortOrder,
  };

  next();
};

// Express-validator 验证中间件
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(error => ({
      field: error.type === 'field' ? (error as any).path : error.type,
      message: error.msg,
      value: error.type === 'field' ? (error as any).value : undefined,
    }));

    res.status(400).json({
      success: false,
      error: {
        code: ErrorCodes.VALIDATION_ERROR,
        message: '请求数据验证失败',
        details: errorDetails,
        timestamp: new Date().toISOString(),
      }
    } as ApiResponse);
    return;
  }

  next();
};

// 扩展Request接口以包含分页信息
declare global {
  namespace Express {
    interface Request {
      pagination?: {
        page: number;
        limit: number;
        offset: number;
        sortBy?: string;
        sortOrder: 'asc' | 'desc';
      };
    }
  }
}
