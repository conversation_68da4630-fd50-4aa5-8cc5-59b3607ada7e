"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelApplicationController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
const log_service_1 = require("../services/log.service");
class LevelApplicationController {
    static async getLevelConfigs(req, res) {
        try {
            const levelConfigs = await database_1.prisma.userLevelConfig.findMany({
                orderBy: { level: 'asc' },
                select: {
                    level: true,
                    displayName: true,
                    maxDailyUploads: true,
                    maxFileSize: true,
                    maxStorageSpace: true,
                    canChooseProvider: true,
                    prioritySupport: true,
                    customDomain: true,
                    visibleProviderCount: true,
                }
            });
            const serializedConfigs = levelConfigs.map(config => ({
                ...config,
                maxFileSize: Number(config.maxFileSize),
                maxStorageSpace: Number(config.maxStorageSpace)
            }));
            res.json({
                success: true,
                data: serializedConfigs,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取等级配置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取等级配置失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async submitApplication(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { requestedLevel, reason, contactInfo, businessInfo, expectedUsage, additionalInfo } = req.body;
            if (!requestedLevel || !reason) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '申请等级和申请理由是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, username: true, userLevel: true, email: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const existingApplication = await database_1.prisma.userLevelApplication.findFirst({
                where: {
                    userId,
                    status: 'pending'
                }
            });
            if (existingApplication) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '您已有待处理的等级申请，请等待审核结果',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const validLevels = ['free', 'vip1', 'vip2', 'vip3'];
            if (!validLevels.includes(requestedLevel)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的等级申请',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const levelOrder = { free: 0, vip1: 1, vip2: 2, vip3: 3 };
            if (levelOrder[requestedLevel] <= levelOrder[user.userLevel]) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '只能申请更高等级的会员',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const application = await database_1.prisma.userLevelApplication.create({
                data: {
                    userId,
                    currentLevel: user.userLevel,
                    requestedLevel,
                    reason,
                    contactInfo: contactInfo || null,
                    businessInfo: businessInfo || null,
                    expectedUsage: expectedUsage || null,
                    additionalInfo: additionalInfo || null,
                },
                select: {
                    id: true,
                    currentLevel: true,
                    requestedLevel: true,
                    reason: true,
                    status: true,
                    createdAt: true
                }
            });
            await log_service_1.LogService.logSystemEvent('level_application_submitted', 'info', `用户 ${user.username} 提交了等级申请：${user.userLevel} -> ${requestedLevel}`, {
                userId,
                applicationId: application.id,
                currentLevel: user.userLevel,
                requestedLevel,
                reason: reason.substring(0, 100)
            });
            res.status(201).json({
                success: true,
                data: application,
                message: '等级申请提交成功，请等待管理员审核',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('提交等级申请失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '提交等级申请失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserApplications(req, res) {
        console.log('=== getUserApplications 开始 ===');
        try {
            const userId = parseInt(req.user.id);
            const { page = 1, limit = 10 } = req.query;
            console.log('获取用户申请历史:', { userId, page, limit });
            res.json({
                success: true,
                data: {
                    applications: [],
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: 0,
                        totalPages: 0
                    }
                },
                timestamp: new Date().toISOString()
            });
            console.log('=== getUserApplications 完成 ===');
        }
        catch (error) {
            console.error('获取申请历史失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取申请历史失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async cancelApplication(req, res) {
        try {
            const userId = parseInt(req.user.id);
            const { applicationId } = req.params;
            if (!applicationId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '申请ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const application = await database_1.prisma.userLevelApplication.findFirst({
                where: {
                    id: parseInt(applicationId),
                    userId,
                    status: 'pending'
                }
            });
            if (!application) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '未找到可取消的申请记录',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedApplication = await database_1.prisma.userLevelApplication.update({
                where: { id: parseInt(applicationId) },
                data: {
                    status: 'cancelled',
                    updatedAt: new Date()
                },
                select: {
                    id: true,
                    status: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logSystemEvent('level_application_cancelled', 'info', `用户取消了等级申请`, { userId, applicationId: parseInt(applicationId) });
            res.json({
                success: true,
                data: updatedApplication,
                message: '申请已取消',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('取消申请失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '取消申请失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
}
exports.LevelApplicationController = LevelApplicationController;
//# sourceMappingURL=levelApplication.controller.js.map