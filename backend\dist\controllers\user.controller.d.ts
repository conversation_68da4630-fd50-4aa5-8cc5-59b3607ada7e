import { Request, Response } from 'express';
export declare class UserController {
    static getProfile(req: Request, res: Response): Promise<void>;
    static updateProfile(req: Request, res: Response): Promise<void>;
    static changePassword(req: Request, res: Response): Promise<void>;
    static updateNotificationSettings(req: Request, res: Response): Promise<void>;
    static updateUploadSettings(req: Request, res: Response): Promise<void>;
    static getActivityLogs(req: Request, res: Response): Promise<void>;
    static getLoginHistory(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=user.controller.d.ts.map